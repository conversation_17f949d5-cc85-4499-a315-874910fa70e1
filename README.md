# 🍌 Banana Checklist

**A hybrid productivity and gaming app that makes task management fun through retro-inspired gameplay**

[![CI](https://github.com/dlkesterson/banana-checklist/actions/workflows/ci.yml/badge.svg)](https://github.com/dlkesterson/banana-checklist/actions/workflows/ci.yml)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.8-blue.svg)](https://www.typescriptlang.org/)
[![Svelte](https://img.shields.io/badge/Svelte-5.35-orange.svg)](https://svelte.dev/)
[![PixiJS](https://img.shields.io/badge/PixiJS-8.11-green.svg)](https://pixijs.com/)

Banana Checklist combines intuitive task tracking with nostalgic 80s/90s-style gameplay. Navigate a pixel-art monkey through a retro-inspired jungle, collect bananas by completing tasks, and unlock advanced productivity features through engaging game mechanics.

## ✨ Features

### 🎯 **Productivity Core**
- **Smart Task Management**: Add, edit, and organize tasks with categories, priorities, and due dates
- **Goal Tracking**: Set long-term goals with milestone tracking and progress visualization
- **Habit Streaks**: Build consistent habits with streak tracking and rewards
- **Progressive Unlocks**: Earn advanced features (analytics, collaboration, export) through gameplay
- **"Act Busy" Button**: Generate random tasks for quick banana rewards when you need a productivity boost

### 🎮 **Retro Gaming Experience**
- **16-bit Pixel Art**: Nostalgic jungle environment with animated monkey character
- **Multiple Game Modes**:
  - **Platformer Navigation**: Jump and climb through jungle platforms (Donkey Kong-inspired)
  - **Top-down Exploration**: Discover hidden groves and secrets (Zelda-inspired)
  - **Banana Sorting**: Tetris-style minigame for organizing tasks
  - **Banana Defense**: Space Invaders-style tower defense
- **Banana Economy**: Earn bananas by completing tasks, spend them on upgrades and features
- **Quests & Achievements**: Daily challenges and milestone rewards
- **Customization**: Unlock monkey skins, hats, and jungle themes

### 🌟 **Freemium Model**
- **Free Tier**: Full access to core features, unlock advanced ones through gameplay
- **Premium Tier**: Instant access to all features plus exclusive cosmetics
- **No Pay-to-Win**: Everything can be unlocked through regular use

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm, pnpm, or yarn

### Installation

```bash
# Clone the repository
git clone https://github.com/dlkesterson/banana-checklist.git
cd banana-checklist

# Install dependencies
npm install

# Start development server
npm run dev

# Open in browser
npm run dev -- --open
```

### Building for Production

```bash
# Create production build
npm run build

# Preview production build
npm run preview
```

## 🧪 Testing

The project includes comprehensive testing with high coverage requirements:

```bash
# Run all tests
npm run test

# Run tests with UI
npm run test:ui

# Run tests with coverage (90% threshold)
npm run test:coverage

# Run end-to-end tests
npm run test:e2e
```

## 🏗️ Tech Stack

- **Frontend Framework**: [SvelteKit](https://kit.svelte.dev/) - Modern, reactive web framework
- **Game Engine**: [PixiJS 8](https://pixijs.com/) - High-performance 2D WebGL renderer
- **Language**: [TypeScript](https://www.typescriptlang.org/) - Type-safe JavaScript
- **Testing**: [Vitest](https://vitest.dev/) + [Playwright](https://playwright.dev/) - Unit and E2E testing
- **Styling**: CSS with component-scoped styles
- **Build Tool**: [Vite](https://vitejs.dev/) - Fast build tool and dev server

## 📁 Project Structure

```
src/
├── lib/
│   ├── components/     # Svelte UI components
│   ├── game/          # PixiJS game logic and systems
│   ├── stores/        # Svelte stores for state management
│   ├── types/         # TypeScript type definitions
│   └── utils/         # Shared utilities and helpers
├── routes/            # SvelteKit pages and API routes
└── test/              # Test files and test utilities

docs/                  # Project documentation
static/                # Static assets (sprites, sounds, etc.)
```

## 🎮 Game Mechanics

### Core Gameplay Loop
1. **Add Tasks**: Create real tasks or use the "Act Busy" button for quick rewards
2. **Navigate Jungle**: Control your monkey through platformer or exploration modes
3. **Collect Bananas**: Earn bananas by completing tasks and finding secrets
4. **Spend & Upgrade**: Use bananas to unlock app features and game improvements
5. **Progress**: Unlock new areas, cosmetics, and productivity tools

### Banana Economy
- **Task Completion**: 5-50 bananas depending on task complexity
- **Daily Quests**: Bonus banana rewards for consistency
- **Minigames**: Additional banana earning opportunities
- **Feature Unlocks**: 100-1000 bananas for advanced productivity features
- **Cosmetics**: Various prices for monkey customization

### Retro-Inspired Minigames
- **Banana Sorting (Tetris-style)**: Sort falling bananas into category baskets
- **Banana Defense (Space Invaders-style)**: Protect your banana stash from bandits
- **Exploration Mode (Zelda-style)**: Discover hidden areas and secrets
- **Platformer Challenges (Donkey Kong-style)**: Navigate obstacle courses

## 🛠️ Development

### Available Scripts

```bash
# Development
npm run dev              # Start dev server
npm run build           # Production build
npm run preview         # Preview production build

# Code Quality
npm run check           # TypeScript type checking
npm run lint            # ESLint code linting
npm run lint:fix        # Auto-fix linting issues

# Testing
npm run test            # Run unit tests
npm run test:ui         # Test with UI interface
npm run test:run        # Run tests once
npm run test:coverage   # Generate coverage report
npm run test:e2e        # End-to-end tests
npm run test:e2e:ui     # E2E tests with UI
npm run test:e2e:debug  # Debug E2E tests
```

### Code Quality Standards
- **TypeScript**: Strict type checking enabled
- **ESLint**: Comprehensive linting rules for code consistency
- **Test Coverage**: 90% minimum coverage requirement
- **CI/CD**: Automated testing and deployment via GitHub Actions

### Contributing Guidelines
1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes with tests
4. Ensure all tests pass (`npm run test`)
5. Commit your changes (`git commit -m 'Add amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

## 📚 Documentation

Detailed documentation is available in the `/docs` folder:

- **[Product Requirements Document](docs/banana_checklist_prd.md)**: Complete feature specifications
- **[Banana Farm Feature](docs/banana-farm-prd.md)**: Idle game mechanics and NPC system
- **[CI/CD Setup](docs/ci-setup.md)**: Continuous integration configuration
- **[Phase 2 Ideas](docs/phase-2-ideas.md)**: Future feature roadmap
- **[Tileset Integration](docs/tileset-integration-guide.md)**: Game asset management

## 🎯 Roadmap

### Phase 1: MVP ✅
- [x] Basic task management (CRUD operations)
- [x] Core platformer game mechanics
- [x] Banana collection and spending system
- [x] "Act Busy" feature for quick rewards
- [x] Local storage with persistent state

### Phase 2: Enhanced Features 🚧
- [ ] Exploration mode with quest system
- [ ] Additional minigames (sorting, defense)
- [ ] Advanced productivity features (analytics, habits)
- [ ] Banana Farm scene with idle mechanics
- [ ] NPC interactions and upgrade system

### Phase 3: Polish & Launch 📋
- [ ] Premium subscription integration
- [ ] Cloud sync capabilities
- [ ] Advanced collaboration features
- [ ] Performance optimization
- [ ] Mobile app deployment

### Phase 4: Post-Launch 🔮
- [ ] Multiplayer features
- [ ] AI-powered task suggestions
- [ ] Additional game modes
- [ ] Community features

## 🤝 Contributing

We welcome contributions! Whether you're interested in:
- 🎮 Game mechanics and PixiJS development
- 🎨 Pixel art and visual design
- 📱 UI/UX improvements
- 🧪 Testing and quality assurance
- 📖 Documentation

Please see our [Contributing Guidelines](CONTRIBUTING.md) for more details.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Inspired by classic 80s/90s games like Donkey Kong, Zelda, and Tetris
- Built with modern web technologies for cross-platform compatibility
- Designed to make productivity fun and engaging through gamification

---

**Made with 🍌 and ❤️ by the Banana Checklist team**

*Turn your to-do list into an adventure!*
