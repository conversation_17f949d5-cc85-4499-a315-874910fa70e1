
# Smart Camera Implementation Plan

## 1. Analyze Existing Game Structure

First, review the current code to identify where the Pixi `Application`, scenes, and game loop are initialized. In a SvelteKit+Pixi project this is often in a Svelte component (e.g. `Game.svelte` or `Scene.js`) with an `onMount` hook that creates `new PIXI.Application` and adds sprites. Ensure there is a clear separation between the **world container** (holding the map, monkey, and game objects) and any UI overlay. A common pattern is to have two containers on the stage: one “world” container that moves with the camera, and one fixed container for HUD or UI. Identify where the monkey sprite’s position is updated (e.g. in input or character-control code) and where the main `app.ticker` loop runs.

## 2. Setup Camera (World) Container

Create or confirm a dedicated Pixi `Container` for the game world (often called `worldContainer`, `level`, or similar). All map tiles, the monkey sprite, and other game entities should be children of this container. For example, load the map texture and add it to `worldContainer`, then add the monkey sprite on top. Finally add `worldContainer` to `app.stage` (the root). This matches the common approach where the camera simply moves the world container opposite to player movement. If not already done, also create a second container for any fixed UI. Keeping UI separate means camera movement won’t affect score displays or menus.

## 3. Compute Camera Target (with Look-Ahead)

Each frame, determine where the camera **should** focus. The basic target is the monkey’s position:

* `targetX = monkey.x`, `targetY = monkey.y`.

For look-ahead (showing more space ahead of motion), offset the target in the monkey’s movement direction. For example, if the monkey has a velocity vector `(vx, vy)`, you might add `targetX += LOOKAHEAD_DISTANCE * sign(vx)` and similarly for Y. This way the camera “looks” further where the monkey is moving. Optionally implement a **dead zone** (a rectangle around the monkey) so that small movements don’t immediately move the camera: only update `targetX/Y` when the monkey leaves this zone. This makes the camera feel less jittery. The look-ahead and dead-zone logic can be tuned to the game’s pace – e.g. larger dead zone for slower movement.

## 4. Apply Smooth Follow (Interpolation)

Instead of snapping the camera instantly to the target, interpolate its position each frame. A simple yet effective technique is linear interpolation (lerp). For example, in the game loop:

```js
cameraX += (targetX - cameraX) * 0.1 * delta;
cameraY += (targetY - cameraY) * 0.1 * delta;
```

Here `delta` is the frame time, and `0.1` is the smoothing factor. This makes the camera catch up gradually (about 10% closer each frame). In practice, you would update `cameraX/Y` after the monkey moves. One PixiJS project describes a “Camera class” that stores an (x,y) offset and updates the level container’s position each tick using such offsets. This is essentially the same idea: compute smoothed offsets and then move the world container accordingly.

## 5. Clamp Camera to Map Boundaries

After computing the new camera position, clamp it so the viewport never shows beyond the map edges. For example, if the viewport width is `vw` and the map width is `mapWidth`, enforce:

```js
cameraX = Math.max(vw/2, Math.min(cameraX, mapWidth - vw/2));
```

(and similarly for `cameraY` with height). If you use the pivot technique, clamp `worldContainer.pivot.x/y` within those limits; if you move the container by negative offset (e.g. `worldContainer.x = vw/2 - cameraX`), clamp `cameraX` first. Clamping ensures that when the monkey is near a border, the camera stops scrolling and the border aligns with the screen edge (preventing “blank” areas). Many camera libraries (e.g. *pixi-viewport*) have built-in clamp support, but it can also be done manually with `Math.max/Math.min`.

## 6. Update Container Position Each Frame

Finally, apply the computed camera position to the world container. There are two common methods:

* **Pivot Method:** Set the container’s pivot to `(cameraX, cameraY)` and position to the screen center. For instance:

  ```js
  app.stage.pivot.set(cameraX, cameraY);
  app.stage.position.set(vw/2, vh/2);
  ```

  This effectively pins the camera at the monkey’s position.

* **Offset Method:** Keep `pivot = (0,0)` and instead set `worldContainer.x = vw/2 - cameraX; worldContainer.y = vh/2 - cameraY;`. This shifts the entire world container so that `(cameraX, cameraY)` appears at the screen center.

Both methods achieve the same result. In your game loop (e.g. inside `app.ticker.add()`), after updating the monkey’s `x,y`, perform the smoothing/clamp logic and then set the container’s pivot/position accordingly each tick. This aligns with the strategy described in many PixiJS examples: move the stage/world opposite to player movement.

## 7. Integration with Code and File References

* **Scene Initialization (e.g. `Game.svelte` or main loop):** Ensure Pixi.Application is set up and containers are created in `onMount`. In this file, after loading assets and adding the map and monkey to `worldContainer`, initialize camera variables (e.g. `cameraX`, `cameraY`) and smoothing parameters.
* **Game Loop:** In the file where you use `app.ticker.add()`, integrate the camera update after character movement. For example, in `Game.svelte` or `gameLoop.js`, do something like:

  ```js
  app.ticker.add((delta) => {
    monkey.update(delta);       // existing character movement
    updateCamera(monkey, delta); // implement steps 3–6 here
    renderer.render(app.stage);
  });
  ```
* **Camera Module:** You may create a separate module (e.g. `Camera.js/Camera.ts`) to encapsulate the logic. It would export a function or class with methods like `setTarget(x,y)`, `update(delta)`, and internally handle lerping and clamping. This keeps code organized and allows re-use.
* **File Organization:** If the repository has a structure like `src/routes/Game.svelte`, `src/lib/Monkey.ts`, etc., integrate camera code into the route or game manager. For example, after the monkey movement code in `Monkey.ts`, call `Camera.follow(monkey.position)`. Ensure `Camera.update()` is called each tick.
* **Clean Architecture:** As suggested by one PixiJS developer, use two containers on the stage: one for moving the world and one for fixed GUI elements. This might involve minor refactoring if the code initially put everything on `app.stage`.

## 8. Suggested Improvements

* **Camera Class:** Refactor camera logic into its own class. This mirrors good practice (and the approach in \[46]) and makes tuning easier. The class can track `cameraX/Y`, smoothing factors, and provide a method to apply its transform to the Pixi container.
* **Use Pixi-Viewport:** Consider using the [`pixi-viewport`](https://github.com/davidfig/pixi-viewport) plugin. It provides a configurable camera out-of-the-box with support for following a target, smoothing, zooming, and automatic clamping to world bounds. Replacing custom code with this library can greatly simplify the implementation and add features like mouse dragging, wheel-zoom, or inertia.
* **Optimize Update Order:** Ensure camera updates happen after character movement and before rendering. If the current loop mixes rendering and logic, separate concerns (character input -> game logic -> camera update -> render).
* **Type Safety:** If using TypeScript, define interfaces for the camera and world container to avoid runtime errors when setting positions/pivots.

## 9. Optional Enhancements

* **Zooming:** Allow dynamic zoom of the camera (e.g. `worldContainer.scale.set(zoom, zoom)`). You might auto-zoom out when the monkey runs fast or enable user control (mouse wheel or pinch). If zooming, always re-clamp boundaries since the visible area changes. The `pixi-viewport` plugin can handle wheel zoom with clamping.
* **Dead Zone / Focus Zone:** Implement a central dead zone so the camera only moves when the monkey exits it; this prevents constant tiny adjustments. Alternatively, define “focus zones” (regions where the camera temporarily locks or shifts viewpoint for gameplay effect). These zones can be checked each frame and override normal follow behavior.
* **Adaptive Look-Ahead:** Vary the look-ahead distance based on speed or context (e.g., increase look-ahead on high speed, or zero it when jumping). Smoothly interpolate the look-ahead offset to avoid jarring shifts.
* **Camera Shake/Effects:** For actions like collisions, you could add a temporary screen shake or tweens on the container for effect. This would layer on top of the normal camera position. The `pixi-viewport` plugin also supports “bouncing” or shake effects if needed.

Each enhancement should reuse the camera update framework. For example, if adding zoom, recalc `vw`/`vh` each frame; if adding dead zone, modify how `targetX/Y` are computed. Testing different parameters (smoothing factor, look-ahead distance) will be key to getting a “feel” that matches your game.

## References

* Follow and interpolation: Use linear interpolation (lerp) for smooth camera movement.
* Container movement: In PixiJS, the camera effect is achieved by moving the world container (or changing the stage pivot) opposite to player movement.
* Separation of GUI: Keep UI elements in a separate container so they stay fixed while the world scrolls.
* Pixi Viewport: The `pixi-viewport` library provides built-in follow, clamp, and zoom support for 2D cameras.
* Example implementation: One Pixi project uses a `Camera` class that updates the level container’s position each frame based on offset values, centering on the player.
