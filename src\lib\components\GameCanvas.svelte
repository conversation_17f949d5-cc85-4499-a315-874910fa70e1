<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { browser } from '$app/environment';
  import * as PIXI from 'pixi.js';
  import { get } from 'svelte/store';
  import { gameStore, userStore } from '$lib/stores';
  import { updateInputFromKeyboard, createBananaParticles, updateParticles, type Particle } from '$lib/utils/gameUtils';
  import { TilesetManager, WorldBuilder, JUNGLE_TILESET_CONFIG } from '$lib/utils/tileUtils';
  import { createJungleBackground } from '$lib/utils/jungleBackground';
  import { NPCManager, createBananaBotNPC } from '$lib/game/NPCManager';
  import { CompanionManager } from '$lib/game/CompanionManager';
  import { createFarmerCompanion } from '$lib/game/companions/FarmerCompanion';
  import { Camera, type CameraConfig } from '$lib/game/Camera';
  import type { NPC } from '$lib/types';

  // Props
  export let debugMode = false;

  // Canvas element reference
  let canvasContainer: HTMLDivElement;
  let pixiApp: PIXI.Application;
  let gameInitialized = false;

  // Screen dimensions
  let screenWidth = 800;
  let screenHeight = 600;
  
  // Game objects
  let monkey: PIXI.AnimatedSprite;
  const idleFrames: PIXI.Texture[] = [];
  const runFrames: PIXI.Texture[] = [];
  const jumpFrames: PIXI.Texture[] = [];
  const bananaFrames: PIXI.Texture[] = [];
  const bananas: PIXI.AnimatedSprite[] = [];
  let particles: Particle[] = [];
  let particleContainer: PIXI.Container;

  // Tileset and world management
  let tilesetManager: TilesetManager;
  let worldBuilder: WorldBuilder;
  let worldContainer: PIXI.Container;
  let uiContainer: PIXI.Container;
  let debugContainer: PIXI.Container;

  // Camera system
  let camera: Camera;

  // Debug timing
  let lastDebugLogTime = 0;

  // Background management
  let jungleBackground: ReturnType<typeof createJungleBackground> | null = null;

  // NPC management
  let npcManager: NPCManager | null = null;

  // Companion management
  let companionManager: CompanionManager | null = null;

  // Input handling
  const keysPressed = new Set<string>();

  // Game state
  const monkeyVelocity = { x: 0, y: 0 };
  const GRAVITY = 0.5;
  const JUMP_FORCE = -12;
  const MOVE_SPEED = 5;
  let GROUND_Y = 500;

  // Scene boundary configuration
  interface SceneBoundaries {
    left: number;
    right: number;
    top: number;
    bottom: number;
  }

  const SCENE_BOUNDARIES: Record<string, SceneBoundaries> = {
    'jungle': {
      left: 0,
      right: 1600, // 2x screen width (800 * 2) - more reasonable size
      top: 0,
      bottom: 900 // 1.5x screen height (600 * 1.5)
    },
    'banana-farm': {
      left: 0,
      right: 1600, // Same width as jungle
      top: 0,
      bottom: 900 // Same height as jungle
    }
  };

  // Scene transition zones (where transitions can occur)
  type BoundaryDirection = 'left' | 'right' | 'top' | 'bottom';

  const SCENE_TRANSITIONS = {
    'jungle-to-farm': {
      scene: 'jungle' as const,
      boundary: 'right' as BoundaryDirection,
      threshold: 50, // Distance from boundary to trigger transition
      targetScene: 'banana-farm' as const,
      targetPosition: { x: 100, y: null } // null means keep current Y
    },
    'farm-to-jungle': {
      scene: 'banana-farm' as const,
      boundary: 'left' as BoundaryDirection,
      threshold: 50,
      targetScene: 'jungle' as const,
      targetPosition: { x: 1500, y: null } // Near right edge of jungle (1600-100)
    }
  };

  // Toast message state
  let showToast = true;
  let toastVisible = false;
  let toastTimeout: ReturnType<typeof setTimeout>;

  // Dialogue state
  let showDialogue = false;
  let currentNPC: NPC | null = null;
  let currentDialogueId = 'greeting';
  let monkeyState: 'idle' | 'moving' | 'jumping' = 'idle';

  // Game objects references for resizing
  let ground: PIXI.Sprite;

  onMount(async () => {
    if (browser) {
      updateScreenDimensions();
      await initializeGame();
      window.addEventListener('resize', handleResize);

      // Show welcome toast
      showWelcomeToast();

      // Add global debug functions for browser console
      (window as any).debugCleanupFarmers = () => {
        if (pixiApp && pixiApp.stage) {
          const farmerSprites = pixiApp.stage.children.filter(child =>
            child instanceof PIXI.AnimatedSprite && child.zIndex === 90
          );
          console.log(`Found ${farmerSprites.length} farmer sprites:`, farmerSprites);
          farmerSprites.forEach((sprite, index) => {
            console.log(`Farmer sprite ${index}:`, sprite);
            if (sprite.parent) {
              pixiApp.stage.removeChild(sprite);
              sprite.destroy();
            }
          });
          console.log('Cleanup complete');
        }
      };

      (window as any).debugCountFarmers = () => {
        if (pixiApp && pixiApp.stage) {
          const farmerSprites = pixiApp.stage.children.filter(child =>
            child instanceof PIXI.AnimatedSprite && child.zIndex === 90
          );
          console.log(`Current farmer sprites: ${farmerSprites.length}`, farmerSprites);
          return farmerSprites.length;
        }
        return 0;
      };

      (window as any).debugAllSprites = () => {
        if (pixiApp && pixiApp.stage) {
          const allSprites = pixiApp.stage.children.filter(child =>
            child instanceof PIXI.AnimatedSprite
          );
          console.log(`All animated sprites on stage: ${allSprites.length}`);
          allSprites.forEach((sprite, index) => {
            console.log(`Sprite ${index}:`, {
              zIndex: sprite.zIndex,
              x: sprite.x,
              y: sprite.y,
              width: sprite.width,
              height: sprite.height,
              visible: sprite.visible,
              textures: sprite.textures?.length || 0
            });
          });
          return allSprites;
        }
        return [];
      };
    }
  });

  onDestroy(() => {
    cleanup();
    if (browser) {
      window.removeEventListener('resize', handleResize);
    }
    // Clear toast timeout
    if (toastTimeout) {
      clearTimeout(toastTimeout);
    }
  });

  // Toast message functions
  function showWelcomeToast() {
    showToast = true;
    // Animate in
    setTimeout(() => {
      toastVisible = true;
    }, 100);

    // Auto-hide after 10 seconds
    toastTimeout = setTimeout(() => {
      hideToast();
    }, 10000);
  }

  function hideToast() {
    toastVisible = false;
    // Remove from DOM after animation
    setTimeout(() => {
      showToast = false;
    }, 500);
    if (toastTimeout) {
      clearTimeout(toastTimeout);
    }
  }

  function handleToastInteraction() {
    if (showToast) {
      hideToast();
    }
  }

  function updateScreenDimensions() {
    screenWidth = window.innerWidth;
    screenHeight = window.innerHeight - 60; // Account for header height
  }

  function handleResize() {
    updateScreenDimensions();
    if (pixiApp && gameInitialized) {
      pixiApp.renderer.resize(screenWidth, screenHeight);

      // Update animated background
      if (jungleBackground) {
        jungleBackground.resize();
      }

      // Update ground width and position to match new screen dimensions
      updateGroundWidth();
      // Update ground Y position
      // GROUND_Y represents where the monkey stands (top of ground tiles)
      GROUND_Y = screenHeight - 64; // 64 = 32px tile height * 2 scale
      if (ground) {
        ground.y = GROUND_Y + 12;
      }

      // Recreate the world with new dimensions
      if (worldBuilder && tilesetManager && tilesetManager.isLoaded()) {
        createBasicWorld();
      }

      // Update camera configuration for new screen size
      if (camera) {
        camera.updateConfig({
          viewportWidth: screenWidth,
          viewportHeight: screenHeight,
          worldWidth: 1600, // Match scene boundary width
          worldHeight: 900 // Match scene boundary height
        });
      }
    }
  }

  function updateGroundWidth() {
    if (ground) {
      ground.width = screenWidth;
    }
  }

  async function initializeGame() {
    // Prevent multiple initializations
    if (gameInitialized) {
      console.log('Game already initialized, skipping...');
      return;
    }

    // Additional check: if pixiApp already exists, clean it up first
    if (pixiApp) {
      console.log('PixiJS app already exists, cleaning up first...');
      cleanup();
    }

    try {
      // Create PixiJS application
      pixiApp = new PIXI.Application();
      await pixiApp.init({
        width: screenWidth,
        height: screenHeight,
        backgroundAlpha: 0, // Transparent background so we can see our animated background
        antialias: true,
        resizeTo: canvasContainer
      });

      // Add canvas to container
      // eslint-disable-next-line svelte/no-dom-manipulating
      canvasContainer.appendChild(pixiApp.canvas);

      // Create game objects
      await createGameObjects();
      
      // Set up input handling
      setupInputHandling();
      
      // Start game loop
      pixiApp.ticker.add(gameLoop);

      gameInitialized = true;
      // eslint-disable-next-line no-console
      console.log('Game initialized successfully!');
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to initialize game:', error);
    }
  }

  async function createGameObjects() {
    try {
      // Enable z-index sorting on the main stage
      pixiApp.stage.sortableChildren = true;

      // Create main containers
      // 1. Background container (for parallax background)
      // 2. World container (for all game objects that move with camera)
      // 3. UI container (for fixed UI elements)

      // Create animated jungle background first (so it's behind everything)
      jungleBackground = createJungleBackground(pixiApp);
      pixiApp.stage.addChild(jungleBackground.container);

      // Create world container for all camera-affected objects
      worldContainer = new PIXI.Container();
      worldContainer.sortableChildren = true;
      worldContainer.zIndex = 10; // World renders after background
      pixiApp.stage.addChild(worldContainer);

      // Create UI container for fixed UI elements
      uiContainer = new PIXI.Container();
      uiContainer.sortableChildren = true;
      uiContainer.zIndex = 1000; // UI renders on top of everything
      pixiApp.stage.addChild(uiContainer);

      // Initialize tileset manager
      tilesetManager = new TilesetManager(JUNGLE_TILESET_CONFIG);

      // Load sprite sheets and tileset
      // eslint-disable-next-line no-console
      console.log('Loading sprite sheets and tileset...');
      const [idleTexture, runTexture, jumpTexture, bananaTexture] = await Promise.all([
        PIXI.Assets.load('/assets/monkey/Idle.png'),
        PIXI.Assets.load('/assets/monkey/Run.png'),
        PIXI.Assets.load('/assets/monkey/Jump.png'),
        PIXI.Assets.load('/assets/banana/Banana.png'),
        tilesetManager.loadTileset('/assets/Tileset-Spritesheet.png')
      ]);

      // eslint-disable-next-line no-console
      console.log('Idle texture loaded:', idleTexture.width, 'x', idleTexture.height);
      // eslint-disable-next-line no-console
      console.log('Run texture loaded:', runTexture.width, 'x', runTexture.height);
      // eslint-disable-next-line no-console
      console.log('Jump texture loaded:', jumpTexture.width, 'x', jumpTexture.height);
      // eslint-disable-next-line no-console
      console.log('Banana texture loaded:', bananaTexture.width, 'x', bananaTexture.height);

      // Clean up any existing monkey sprites before creating new one
      cleanupOrphanedMonkeySprites();

      // Frame dimensions (32x32 pixels for each frame)
      const frameWidth = 32;
      const frameHeight = 32;

      // Create idle animation frames (18 frames)
      const idleTotalFrames = 18;
      // eslint-disable-next-line no-console
      console.log('Creating idle frames:', idleTotalFrames, 'frames');

      for (let i = 0; i < idleTotalFrames; i++) {
        const frame = new PIXI.Texture({
          source: idleTexture.source,
          frame: new PIXI.Rectangle(i * frameWidth, 0, frameWidth, frameHeight)
        });
        idleFrames.push(frame);
      }

      // Create run animation frames (8 frames)
      const runTotalFrames = 8;
      // eslint-disable-next-line no-console
      console.log('Creating run frames:', runTotalFrames, 'frames');

      for (let i = 0; i < runTotalFrames; i++) {
        const frame = new PIXI.Texture({
          source: runTexture.source,
          frame: new PIXI.Rectangle(i * frameWidth, 0, frameWidth, frameHeight)
        });
        runFrames.push(frame);
      }

      // Create jump animation frames (4 frames)
      const jumpTotalFrames = 4;
      // eslint-disable-next-line no-console
      console.log('Creating jump frames:', jumpTotalFrames, 'frames');

      for (let i = 0; i < jumpTotalFrames; i++) {
        const frame = new PIXI.Texture({
          source: jumpTexture.source,
          frame: new PIXI.Rectangle(i * frameWidth, 0, frameWidth, frameHeight)
        });
        jumpFrames.push(frame);
      }

      // Create banana animation frames (12 frames, 16x16 pixels each)
      const bananaFrameWidth = 16;
      const bananaFrameHeight = 16;
      const bananaTotalFrames = 12;
      // eslint-disable-next-line no-console
      console.log('Creating banana frames:', bananaTotalFrames, 'frames');

      for (let i = 0; i < bananaTotalFrames; i++) {
        const frame = new PIXI.Texture({
          source: bananaTexture.source,
          frame: new PIXI.Rectangle(i * bananaFrameWidth, 0, bananaFrameWidth, bananaFrameHeight)
        });
        bananaFrames.push(frame);
      }

      // Create animated monkey sprite
      monkey = new PIXI.AnimatedSprite(idleFrames);
      monkey.animationSpeed = 0.15; // Animation speed (frames per tick)
      monkey.loop = true;
      monkey.play();

      // Set anchor to center so sprite flips around its center point
      monkey.anchor.set(0.5, 0.5);

      // Scale the sprite to 2x size (32x32 becomes 64x64)
      monkey.scale.set(2, 2);

      // Position the sprite (accounting for 2x scale and center anchor)
      monkey.x = 100 + (frameWidth * 1); // Add half width since anchor is centered
      monkey.y = GROUND_Y - (frameHeight * 1); // Add half height since anchor is centered
      monkey.zIndex = 100; // Ensure monkey renders in front
      worldContainer.addChild(monkey);

      // Position companion near monkey after monkey is created
      if (companionManager) {
        const activeCompanion = companionManager.getActiveCompanion();
        if (activeCompanion) {
          // With camera system, both monkey and companion use world coordinates
          // Monkey uses center anchor (0.5, 0.5), farmer uses bottom-center anchor (0.5, 1.0)
          // To align their feet, farmer's Y should be at monkey's bottom edge
          const monkeyHeight = 64; // 32x32 scaled to 2x = 64x64
          const targetY = monkey.y + monkeyHeight/2; // Monkey's bottom edge (feet level)
          activeCompanion.setPosition(monkey.x - 60, targetY);

          // Update the companion's ground reference to match monkey's position
          if ('setWorldBuilder' in activeCompanion) {
            (activeCompanion as any).setWorldBuilder(worldBuilder, targetY);
          }
        }
      }

      // eslint-disable-next-line no-console
      console.log('Monkey animated sprite created successfully!');
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to load monkey sprite:', error);

      // Fallback to simple rectangle if sprite loading fails
      monkey = new PIXI.AnimatedSprite([PIXI.Texture.WHITE]);
      monkey.width = 32;
      monkey.height = 32;
      monkey.tint = 0x8B4513; // Brown color for monkey

      // Set anchor to center for consistent flipping behavior
      monkey.anchor.set(0.5, 0.5);

      monkey.scale.set(2, 2);
      monkey.x = 100 + 32; // Add half width since anchor is centered
      monkey.y = GROUND_Y - 32; // Add half height since anchor is centered
      monkey.zIndex = 100; // Ensure monkey renders in front
      worldContainer.addChild(monkey);

      // Position companion near monkey after monkey is created (fallback case)
      if (companionManager) {
        const activeCompanion = companionManager.getActiveCompanion();
        if (activeCompanion) {
          const monkeyHeight = 64; // 32x32 scaled to 2x = 64x64
          const targetY = monkey.y + monkeyHeight/2; // Align feet level
          activeCompanion.setPosition(monkey.x - 60, targetY);
        }
      }
    }

    // Update ground Y position based on screen height FIRST
    // GROUND_Y represents where the monkey stands (top of ground tiles)
    GROUND_Y = screenHeight - 64; // 64 = 32px tile height * 2 scale

    // Initialize world builder and create basic world
    if (tilesetManager.isLoaded()) {
      worldBuilder = new WorldBuilder(tilesetManager);
      const tileContainer = worldBuilder.getContainer();
      tileContainer.zIndex = 50; // World elements render behind monkey but in front of background
      worldContainer.addChild(tileContainer);

      // Create a simple world for now
      createBasicWorld();

      // eslint-disable-next-line no-console
      console.log('World created successfully!');
    } else {
      // eslint-disable-next-line no-console
      console.warn('Tileset not loaded, skipping world creation');
    }

    // Provide world container to the app for NPC and companion access
    (pixiApp as any).worldContainer = worldContainer;

    // Initialize NPC manager after world container is available
    npcManager = new NPCManager(pixiApp);

    // Initialize companion manager
    console.log('Creating CompanionManager...');
    companionManager = new CompanionManager(pixiApp);

    // Add farmer companion and set it as active
    console.log('Creating farmer companion...');
    const farmerCompanion = createFarmerCompanion(pixiApp);
    console.log('Adding farmer companion to manager...');
    companionManager.addCompanion(farmerCompanion, 'farmer');
    console.log('Setting farmer as active companion...');
    await companionManager.setActiveCompanion('farmer');

    // Set worldBuilder for farmer physics if available
    // With camera system, use the monkey's Y position as the ground reference
    if (worldBuilder && farmerCompanion.setWorldBuilder && monkey) {
      console.log('Setting worldBuilder and ground reference for farmer. Monkey Y:', monkey.y);
      farmerCompanion.setWorldBuilder(worldBuilder, monkey.y);
    }

    // Create particle container (in world so it moves with camera)
    particleContainer = new PIXI.Container();
    particleContainer.zIndex = 200; // Particles render on top of everything in world
    worldContainer.addChild(particleContainer);

    // Create debug container (in UI so it stays fixed)
    debugContainer = new PIXI.Container();
    debugContainer.zIndex = 300; // Debug info renders on top of everything
    uiContainer.addChild(debugContainer);

    // Create some bananas to collect
    createBananas();

    // Create ground (simple rectangle as fallback)
    if (!tilesetManager || !tilesetManager.isLoaded()) {
      ground = new PIXI.Sprite(PIXI.Texture.WHITE);
      ground.width = screenWidth;
      ground.height = 20;
      ground.tint = 0x654321; // Brown ground
      ground.x = 0;
      ground.y = GROUND_Y + 12;
      ground.zIndex = 25; // Ground renders behind most elements but in front of background
      worldContainer.addChild(ground);
    }

    // Initialize camera system
    initializeCamera();
  }

  /**
   * Initialize camera system
   */
  function initializeCamera() {
    // Define world bounds to match scene boundaries
    const worldWidth = 1600; // Match scene boundary width
    const worldHeight = 900; // Match scene boundary height

    const cameraConfig: CameraConfig = {
      smoothingFactor: 0.08, // Slightly smoother camera movement for platformer feel
      lookAheadDistance: 120, // Look ahead 120 pixels in movement direction
      deadZoneWidth: 80, // Smaller dead zone for more responsive camera
      deadZoneHeight: 60, // Smaller dead zone height
      worldWidth: worldWidth,
      worldHeight: worldHeight,
      viewportWidth: screenWidth,
      viewportHeight: screenHeight
    };

    camera = new Camera(worldContainer, cameraConfig);

    // Initialize camera position to monkey's position
    if (monkey) {
      camera.setPosition(monkey.x, monkey.y);
      console.log(`Camera initialized at position (${monkey.x}, ${monkey.y}) with world bounds ${worldWidth}x${worldHeight}`);
    } else {
      console.log('Camera initialized but monkey not found');
    }
  }

  /**
   * Show NPC dialogue interface
   */
  function showNPCDialogue(npc: NPC) {
    currentNPC = npc;
    currentDialogueId = 'greeting';
    showDialogue = true;
  }

  /**
   * Close dialogue interface
   */
  function closeDialogue() {
    showDialogue = false;
    currentNPC = null;
    currentDialogueId = 'greeting';
  }

  /**
   * Clean up any orphaned monkey sprites that might be left in containers
   */
  function cleanupOrphanedMonkeySprites() {
    if (!worldContainer) return;

    // Find and remove any monkey sprites with zIndex 100 that aren't the current monkey
    const orphanedMonkeys = worldContainer.children.filter((child: any) =>
      child instanceof PIXI.AnimatedSprite &&
      child.zIndex === 100 &&
      child !== monkey
    );

    orphanedMonkeys.forEach((orphan: any) => {
      console.log('Removing orphaned monkey sprite');
      worldContainer.removeChild(orphan);
      orphan.destroy();
    });

    // Also check stage for any monkey sprites that might be there
    const stageOrphans = pixiApp.stage.children.filter((child: any) =>
      child instanceof PIXI.AnimatedSprite &&
      child.zIndex === 100 &&
      child !== monkey
    );

    stageOrphans.forEach((orphan: any) => {
      console.log('Removing orphaned monkey sprite from stage');
      pixiApp.stage.removeChild(orphan);
      orphan.destroy();
    });
  }

  /**
   * Handle dialogue response selection
   */
  function handleDialogueResponse(responseIndex: number) {
    if (!currentNPC) return;

    const currentDialogue = currentNPC.dialogues.find(d => d.id === currentDialogueId);
    if (!currentDialogue || !currentDialogue.responses || !currentDialogue.responses[responseIndex]) return;

    const response = currentDialogue.responses[responseIndex];

    if (response.action === 'close') {
      closeDialogue();
    } else if (response.action === 'dialogue' && response.target) {
      currentDialogueId = response.target;
    } else if (response.action === 'shop') {
      // TODO: Open shop interface
      alert('Shop interface coming soon!');
    }
  }

  /**
   * Create a basic world using tileset sprites
   */
  function createBasicWorld() {
    if (!worldBuilder || !tilesetManager.isLoaded()) {
      // eslint-disable-next-line no-console
      console.warn('Cannot create world: tileset not loaded');
      return;
    }

    // Clear any existing world
    worldBuilder.clearWorld();

    // Get available tile IDs to work with
    const availableTiles = tilesetManager.getAvailableTileIds();

    if (availableTiles.length === 0) {
      // eslint-disable-next-line no-console
      console.warn('No tiles available in tileset');
      return;
    }

    // Use the first few tiles for basic world elements
    // These IDs will need to be adjusted based on the actual tileset layout
    const grassTileId = availableTiles[0] || 0; // Assume first tile is grass
    const dirtTileId = availableTiles[1] || 1;  // Assume second tile is dirt
    const platformTileId = availableTiles[2] || 2; // Assume third tile is platform

    // Create ground tiles across the screen width
    const tileSize = 32; // 16x16 tiles scaled 2x
    const groundTilesNeeded = Math.ceil(screenWidth / tileSize) + 2; // Extra tiles for smooth scrolling

    // Create main ground level - tiles positioned so their TOP is at GROUND_Y
    for (let i = 0; i < groundTilesNeeded; i++) {
      // Ground tiles at GROUND_Y (where monkey stands)
      worldBuilder.addTile(grassTileId, i * tileSize, GROUND_Y, 2);

      // Add dirt tiles below the grass (extending down from GROUND_Y)
      worldBuilder.addTile(dirtTileId, i * tileSize, GROUND_Y + tileSize, 2);
      worldBuilder.addTile(dirtTileId, i * tileSize, GROUND_Y + tileSize * 2, 2);
    }

    // Create some floating platforms for variety (positioned above ground level)
    const platformY1 = GROUND_Y - 80;  // 80px above ground (about 2.5 tile heights)
    const platformY2 = GROUND_Y - 140; // 140px above ground (about 4.5 tile heights)

    // Platform 1 (left side)
    worldBuilder.createPlatform(platformTileId, screenWidth * 0.2, platformY1, 4, 2);

    // Platform 2 (center, higher)
    worldBuilder.createPlatform(platformTileId, screenWidth * 0.5, platformY2, 3, 2);

    // Platform 3 (right side)
    worldBuilder.createPlatform(platformTileId, screenWidth * 0.75, platformY1, 3, 2);

    // eslint-disable-next-line no-console
    console.log(`Created world with ${groundTilesNeeded} ground tiles and 3 platforms`);
  }

  /**
   * Create the jungle scene (default scene)
   */
  function createJungleScene() {
    if (!worldBuilder || !tilesetManager.isLoaded()) return;

    // Clear existing world
    worldBuilder.clearWorld();

    // Clear NPCs (jungle scene has no NPCs currently)
    if (npcManager) {
      npcManager.clearAllNPCs();
    }

    // Create jungle world (same as basic world for now)
    createBasicWorld();

    // eslint-disable-next-line no-console
    console.log('Jungle scene created');
  }

  /**
   * Create the banana farm scene
   */
  function createFarmScene() {
    if (!worldBuilder || !tilesetManager.isLoaded()) return;

    // Clear existing world
    worldBuilder.clearWorld();

    // Create farm-themed world
    const grassTileId = 5; // Use grass tiles for farmland
    const stoneTileId = 12; // Use stone for farm structures
    const platformTileId = 8; // Farm platforms

    // Calculate ground tiles needed
    const tileWidth = 32; // Base tile size
    const tileScale = 2; // Scaling factor
    const scaledTileWidth = tileWidth * tileScale;
    const groundTilesNeeded = Math.ceil(screenWidth / scaledTileWidth) + 2; // Extra for smooth scrolling

    // Create farmland ground
    for (let i = 0; i < groundTilesNeeded; i++) {
      const x = i * scaledTileWidth;
      worldBuilder.addTile(grassTileId, x, GROUND_Y, tileScale);
    }

    // Create farm structures - organized rows of platforms for "crop fields"
    const fieldY1 = GROUND_Y - 40;  // Slightly above ground

    // Field rows (representing crop plots)
    for (let row = 0; row < 3; row++) {
      const y = fieldY1 - (row * 60);
      for (let col = 0; col < 4; col++) {
        const x = screenWidth * 0.2 + (col * screenWidth * 0.15);
        worldBuilder.createPlatform(platformTileId, x, y, 2, 1);
      }
    }

    // Farm building structure (right side)
    worldBuilder.createPlatform(stoneTileId, screenWidth * 0.8, GROUND_Y - 100, 3, 4);

    // Clear existing NPCs and add Banana Bot
    if (npcManager) {
      npcManager.clearAllNPCs();

      // Position Banana Bot in world coordinates (not screen coordinates)
      // Place it in the middle-right area of the farm scene world
      const sceneBounds = SCENE_BOUNDARIES['banana-farm'];
      const bananaBotPosition = {
        x: sceneBounds.right * 0.6, // 60% across the farm world width
        y: GROUND_Y - 32 // Position on ground level
      };

      const bananaBotNPC = createBananaBotNPC(bananaBotPosition);
      npcManager.addNPC(bananaBotNPC);

      console.log(`Banana Bot positioned at world coordinates (${bananaBotPosition.x}, ${bananaBotPosition.y})`);

      // Set up NPC interaction callback
      npcManager.setInteractionCallback((npc) => {
        console.log(`Player interacted with ${npc.name}!`);
        showNPCDialogue(npc);
      });
    }

    // eslint-disable-next-line no-console
    console.log('Farm scene created with organized crop fields and Banana Bot NPC');
  }

  // Function to switch monkey animation
  function switchMonkeyAnimation(animationType: 'idle' | 'run' | 'jump') {
    if (!monkey || !idleFrames.length || !runFrames.length || !jumpFrames.length) return;

    const currentScale = { x: monkey.scale.x, y: monkey.scale.y };
    const currentPosition = { x: monkey.x, y: monkey.y };

    // Remove current sprite from world container (not stage)
    if (monkey.parent) {
      monkey.parent.removeChild(monkey);
    }

    // Clean up any orphaned monkey sprites in world container
    cleanupOrphanedMonkeySprites();

    // Create new sprite with appropriate frames
    let frames: PIXI.Texture[];
    let animationSpeed: number;

    switch (animationType) {
      case 'idle':
        frames = idleFrames;
        animationSpeed = 0.15;
        break;
      case 'run':
        frames = runFrames;
        animationSpeed = 0.2;
        break;
      case 'jump':
        frames = jumpFrames;
        animationSpeed = 0.25; // Jump animation slightly faster
        break;
      default:
        frames = idleFrames;
        animationSpeed = 0.15;
    }

    monkey = new PIXI.AnimatedSprite(frames);

    // Set anchor to center for consistent flipping behavior
    monkey.anchor.set(0.5, 0.5);

    // Restore properties
    monkey.scale.set(currentScale.x, currentScale.y);
    monkey.x = currentPosition.x;
    monkey.y = currentPosition.y;
    monkey.animationSpeed = animationSpeed;
    monkey.loop = true;
    monkey.play();

    // Maintain z-index and add back to world container
    monkey.zIndex = 100;
    worldContainer.addChild(monkey);
  }

  function createBananas() {
    // Only create bananas if frames are loaded
    if (bananaFrames.length === 0) {
      // eslint-disable-next-line no-console
      console.warn('Banana frames not loaded yet, skipping banana creation');
      return;
    }

    // Create bananas spread across a wider area to test camera movement
    const bananaPositions = [
      { x: screenWidth * 0.25, y: GROUND_Y - 40 },   // On ground level
      { x: screenWidth * 0.5, y: GROUND_Y - 120 },   // On middle platform
      { x: screenWidth * 0.75, y: GROUND_Y - 50 },   // Slightly above ground
      { x: screenWidth * 0.375, y: GROUND_Y - 160 }, // On high platform
      // Additional bananas for camera testing
      { x: screenWidth * 1.5, y: GROUND_Y - 40 },    // Far right
      { x: screenWidth * 2.0, y: GROUND_Y - 80 },    // Even further right
      { x: screenWidth * 2.5, y: GROUND_Y - 40 },    // Very far right
      { x: screenWidth * 0.1, y: GROUND_Y - 40 },    // Far left
      { x: -screenWidth * 0.2, y: GROUND_Y - 40 },   // Very far left
    ];

    bananaPositions.forEach(pos => {
      // Create animated banana sprite
      const banana = new PIXI.AnimatedSprite(bananaFrames);
      banana.animationSpeed = 0.1; // Slower animation for bananas
      banana.loop = true;
      banana.play();

      // Scale to 2x (16x16 becomes 32x32)
      banana.scale.set(2, 2);

      // Position the banana
      banana.x = pos.x;
      banana.y = pos.y;
      banana.zIndex = 75; // Bananas render in front of world but behind monkey

      bananas.push(banana);
      worldContainer.addChild(banana);
    });
  }

  let canvasFocused = false;

  function setupInputHandling() {
    // Keyboard event listeners - only when canvas is focused
    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    // Canvas focus handling
    if (pixiApp.canvas) {
      pixiApp.canvas.tabIndex = 0; // Make canvas focusable
      pixiApp.canvas.addEventListener('focus', () => {
        canvasFocused = true;
      });
      pixiApp.canvas.addEventListener('blur', () => {
        canvasFocused = false;
      });
      pixiApp.canvas.addEventListener('click', () => {
        pixiApp.canvas.focus();
      });
    }
  }



  function handleCanvasContainerClick() {
    // Hide toast on click
    handleToastInteraction();

    if (pixiApp && pixiApp.canvas) {
      pixiApp.canvas.focus();
      canvasFocused = true; // Force focus state update
    }
  }

  function handleKeyDown(event: KeyboardEvent) {
    // Hide toast on any input
    handleToastInteraction();

    // Only capture game input when canvas is focused and not typing in form fields
    if (!canvasFocused || isTypingInForm(event.target)) {
      return;
    }

    keysPressed.add(event.code);
    event.preventDefault();
  }

  function handleKeyUp(event: KeyboardEvent) {
    // Only capture game input when canvas is focused and not typing in form fields
    if (!canvasFocused || isTypingInForm(event.target)) {
      return;
    }

    keysPressed.delete(event.code);
    event.preventDefault();
  }

  function isTypingInForm(target: EventTarget | null): boolean {
    if (!target || !(target instanceof HTMLElement)) return false;

    const tagName = target.tagName.toLowerCase();
    return tagName === 'input' || tagName === 'textarea' || tagName === 'select' ||
           target.contentEditable === 'true' || target.closest('form') !== null;
  }

  function gameLoop(ticker: PIXI.Ticker) {
    if (!gameInitialized) return;

    // Debug: Count farmer sprites every few frames
    if (ticker.lastTime % 120 < 16) { // Every ~2 seconds at 60fps
      const farmerSprites = pixiApp.stage.children.filter(child =>
        child instanceof PIXI.AnimatedSprite && child.zIndex === 90
      );
      if (farmerSprites.length > 1) {
        console.error(`GAME LOOP: Found ${farmerSprites.length} farmer sprites!`, farmerSprites);
      }
    }

    // Update animated background
    if (jungleBackground) {
      jungleBackground.update(ticker.deltaTime);
    }

    // Update input
    const input = updateInputFromKeyboard({
      left: false,
      right: false,
      up: false,
      down: false,
      jump: false,
      interact: false
    }, keysPressed);

    // Update monkey movement
    updateMonkeyMovement(input, ticker.deltaTime);

    // Check banana collisions
    checkBananaCollisions();

    // Update particles
    updateParticleSystem();

    // Update NPCs
    if (npcManager && monkey) {
      npcManager.update({ x: monkey.x, y: monkey.y });
    }

    // Update companion
    if (companionManager && monkey) {
      // Calculate the correct target position for the companion in world coordinates
      // Monkey uses centered anchor (0.5, 0.5), farmer uses bottom-center anchor (0.5, 1.0)
      // So we need to align their feet at the same ground level
      const monkeyHeight = 64; // 32x32 scaled to 2x = 64x64
      const targetY = monkey.y + monkeyHeight/2; // Monkey's bottom edge (feet level)

      companionManager.update(ticker.deltaTime, { x: monkey.x, y: targetY });

      // Update companion position in store
      const activeCompanion = companionManager.getActiveCompanion();
      if (activeCompanion) {
        const companionPos = activeCompanion.getPosition();
        gameStore.updateCompanionPosition(companionPos.x, companionPos.y);

        // Note: Ground reference is set during initialization and scene transitions
        // No need to update it every frame
      }
    }

    // Update camera to follow monkey
    if (camera && monkey) {
      camera.setTarget({
        x: monkey.x,
        y: monkey.y,
        vx: monkeyVelocity.x,
        vy: monkeyVelocity.y
      });
      camera.update(ticker.deltaTime);

      // Debug: Log camera position and scene info occasionally (only in debug mode)
      const currentTime = Date.now();
      if (debugMode && currentTime - lastDebugLogTime > 3000) { // Every 3 seconds
        lastDebugLogTime = currentTime;
        const cameraPos = camera.getPosition();
        const cameraTarget = camera.getTarget();
        const currentScene = get(gameStore).currentScene;
        const sceneBounds = SCENE_BOUNDARIES[currentScene];
        const transitionThreshold = sceneBounds ? sceneBounds.right - 50 : 0; // Right boundary - threshold
        console.log(`Camera - Position: (${cameraPos.x.toFixed(1)}, ${cameraPos.y.toFixed(1)}), Target: (${cameraTarget.x.toFixed(1)}, ${cameraTarget.y.toFixed(1)}), Monkey: (${monkey.x.toFixed(1)}, ${monkey.y.toFixed(1)})`);
        console.log(`Scene: ${currentScene}, Bounds: ${sceneBounds?.left}-${sceneBounds?.right} x ${sceneBounds?.top}-${sceneBounds?.bottom}, Transition at: ${transitionThreshold}`);
      }
    }

    // Update debug visualization
    updateDebugVisualization();

    // Update stores
    gameStore.updateMonkeyPosition(monkey.x, monkey.y);
  }

  function checkSceneTransitions(monkeyX: number) {
    const currentScene = get(gameStore).currentScene;

    // Debug: Log transition check occasionally (only when crossing 100-pixel boundaries)
    if (debugMode && Math.floor(monkeyX) % 200 === 0 && Math.floor(monkeyX) !== Math.floor(monkeyX - 1)) {
      console.log(`Checking transitions: monkey at ${monkeyX.toFixed(1)}, scene: ${currentScene}`);
      console.log(`Available transitions:`, Object.keys(SCENE_TRANSITIONS));
      console.log(`Scene boundaries for ${currentScene}:`, SCENE_BOUNDARIES[currentScene]);
    }

    // Check each possible transition
    for (const [transitionId, transition] of Object.entries(SCENE_TRANSITIONS)) {
      if (transition.scene !== currentScene) continue;

      const sceneBounds = SCENE_BOUNDARIES[currentScene];
      if (!sceneBounds) continue;

      let shouldTransition = false;

      // Check if monkey is near the specified boundary
      switch (transition.boundary) {
        case 'right':
          shouldTransition = monkeyX > sceneBounds.right - transition.threshold;
          // Debug: Log when close to transition
          if (debugMode && monkeyX > sceneBounds.right - transition.threshold - 100) {
            console.log(`Close to right transition: ${monkeyX.toFixed(1)} > ${sceneBounds.right - transition.threshold} = ${shouldTransition}`);
          }
          break;
        case 'left':
          shouldTransition = monkeyX < sceneBounds.left + transition.threshold;
          break;
        case 'top':
          shouldTransition = monkey.y < sceneBounds.top + transition.threshold;
          break;
        case 'bottom':
          shouldTransition = monkey.y > sceneBounds.bottom - transition.threshold;
          break;
      }

      if (shouldTransition) {
        console.log(`🚀 Scene transition triggered: ${transitionId} at monkey position (${monkeyX.toFixed(1)}, ${monkey.y.toFixed(1)})`);
        console.log(`Scene bounds: left=${sceneBounds.left}, right=${sceneBounds.right}, top=${sceneBounds.top}, bottom=${sceneBounds.bottom}`);
        console.log(`Transition threshold: ${transition.threshold}, boundary: ${transition.boundary}`);

        // Special handling for jungle to farm transition
        if (transitionId === 'jungle-to-farm') {
          // Unlock farm if not already unlocked
          const farmData = get(gameStore).farmData;
          if (!farmData?.isUnlocked) {
            gameStore.unlockFarm();
          }
        }

        // Change to target scene
        gameStore.changeScene(transition.targetScene);

        // Position monkey at target position
        monkey.x = transition.targetPosition.x;
        if (transition.targetPosition.y !== null) {
          monkey.y = transition.targetPosition.y;
        }

        // Position companion near monkey
        if (companionManager) {
          const activeCompanion = companionManager.getActiveCompanion();
          if (activeCompanion) {
            const monkeyHeight = 64; // 32x32 scaled to 2x = 64x64
            const targetY = monkey.y + monkeyHeight/2; // Align feet level
            activeCompanion.setPosition(monkey.x - 60, targetY);
          }
        }

        // Create the appropriate scene
        if (transition.targetScene === 'banana-farm') {
          createFarmScene();
        } else if (transition.targetScene === 'jungle') {
          createJungleScene();
        }

        return;
      }
    }
  }

  function updateMonkeyMovement(input: { left: boolean; right: boolean; up: boolean; jump: boolean }, deltaTime: number) {
    const monkeyHeight = 64; // 32x32 scaled to 2x = 64x64
    const monkeyWidth = 64;

    // Check if monkey is on ground or on a platform
    // Since anchor is centered, monkey.y is center, so bottom is monkey.y + monkeyHeight/2
    let isOnGround = monkey.y + monkeyHeight/2 >= GROUND_Y;

    // Also check if standing on a platform
    if (!isOnGround && worldBuilder) {
      const tilesBelow = worldBuilder.getTilesInArea(
        monkey.x - monkeyWidth/2, // Left edge (anchor is centered)
        monkey.y + monkeyHeight/2 - 5, // Check just below monkey's feet
        monkeyWidth,
        10 // Small area below feet
      );

      for (const tile of tilesBelow) {
        if (tile.sprite) {
          const tileTop = tile.sprite.y;
          const tileLeft = tile.sprite.x;
          const tileRight = tile.sprite.x + tile.sprite.width;

          const monkeyBottom = monkey.y + monkeyHeight/2; // Bottom edge (anchor is centered)
          const monkeyLeft = monkey.x - monkeyWidth/2; // Left edge (anchor is centered)
          const monkeyRight = monkey.x + monkeyWidth/2; // Right edge (anchor is centered)

          // Check if monkey is standing on this tile
          const horizontalOverlap = monkeyRight > tileLeft && monkeyLeft < tileRight;
          const verticalAlignment = Math.abs(monkeyBottom - tileTop) < 5; // Small tolerance

          if (horizontalOverlap && verticalAlignment) {
            isOnGround = true;
            break;
          }
        }
      }
    }

    // Horizontal movement
    if (input.left) {
      monkeyVelocity.x = -MOVE_SPEED;
      monkey.scale.x = -2; // Flip sprite to face left (maintain 2x scale)
    } else if (input.right) {
      monkeyVelocity.x = MOVE_SPEED;
      monkey.scale.x = 2; // Face right (maintain 2x scale)
    } else {
      monkeyVelocity.x *= 0.8; // Friction
    }

    // Jumping
    if (input.jump && isOnGround) {
      monkeyVelocity.y = JUMP_FORCE;
    }

    // Apply gravity
    monkeyVelocity.y += GRAVITY * deltaTime;

    // Update position
    monkey.x += monkeyVelocity.x * deltaTime;
    monkey.y += monkeyVelocity.y * deltaTime;

    // Get monkey dimensions (already defined above)

    // Scene transition checks (account for centered anchor)
    checkSceneTransitions(monkey.x);

    // Note: Boundary constraints are now handled by the camera system
    // The monkey can move freely within the world bounds

    // Platform collision detection
    if (worldBuilder) {
      const tilesInArea = worldBuilder.getTilesInArea(
        monkey.x - monkeyWidth/2, // Left edge (anchor is centered)
        monkey.y - monkeyHeight/2, // Top edge (anchor is centered)
        monkeyWidth,
        monkeyHeight
      );

      // Check for platform collisions (only when falling down)
      if (monkeyVelocity.y > 0) {
        for (const tile of tilesInArea) {
          if (tile.sprite) {
            const tileTop = tile.sprite.y;
            const tileLeft = tile.sprite.x;
            const tileRight = tile.sprite.x + tile.sprite.width;

            // Check if monkey is landing on top of the tile (account for centered anchor)
            const monkeyBottom = monkey.y + monkeyHeight/2; // Bottom edge (anchor is centered)
            const monkeyLeft = monkey.x - monkeyWidth/2; // Left edge (anchor is centered)
            const monkeyRight = monkey.x + monkeyWidth/2; // Right edge (anchor is centered)

            // Horizontal overlap check
            const horizontalOverlap = monkeyRight > tileLeft && monkeyLeft < tileRight;

            // Vertical collision check (landing on top)
            if (horizontalOverlap &&
                monkeyBottom >= tileTop &&
                monkeyBottom <= tileTop + 10 && // Small tolerance for landing
                monkey.y - monkeyHeight/2 < tileTop) { // Monkey's top is above tile's top

              monkey.y = tileTop - monkeyHeight/2; // Position center at correct height
              monkeyVelocity.y = 0;
              break; // Stop checking once we find a collision
            }
          }
        }
      }
    }

    // Ground collision (account for centered anchor)
    const groundLevel = GROUND_Y - monkeyHeight/2;
    if (monkey.y >= groundLevel) {
      monkey.y = groundLevel;
      monkeyVelocity.y = 0;
    }

    // Update monkey state and animation
    const isMoving = Math.abs(monkeyVelocity.x) > 0.1;
    const newState = !isOnGround ? 'jumping' : isMoving ? 'moving' : 'idle';

    if (newState !== monkeyState) {
      monkeyState = newState;

      // Switch animations based on state
      if (monkeyState === 'idle') {
        switchMonkeyAnimation('idle');
      } else if (monkeyState === 'moving') {
        switchMonkeyAnimation('run');
      } else if (monkeyState === 'jumping') {
        switchMonkeyAnimation('jump');
      }
    }
  }

  function checkBananaCollisions() {
    const monkeyWidth = 64;
    const monkeyHeight = 64;

    bananas.forEach((banana) => {
      if (banana.visible &&
          monkey.x - monkeyWidth/2 < banana.x + banana.width &&
          monkey.x + monkeyWidth/2 > banana.x &&
          monkey.y - monkeyHeight/2 < banana.y + banana.height &&
          monkey.y + monkeyHeight/2 > banana.y) {
        
        // Collect banana
        banana.visible = false;
        
        // Add bananas to user store
        userStore.addBananas(5);
        
        // Create particle effect (center of the 2x scaled banana)
        const newParticles = createBananaParticles(banana.x + 16, banana.y + 16, 8);
        particles.push(...newParticles);
        
        // eslint-disable-next-line no-console
        console.log('Banana collected! +5 bananas');
      }
    });
  }

  function updateParticleSystem() {
    // Update existing particles
    particles = updateParticles(particles);

    // Clear particle container
    particleContainer.removeChildren();

    // Render particles
    particles.forEach(particle => {
      const particleSprite = new PIXI.Sprite(PIXI.Texture.WHITE);
      particleSprite.width = 4 * particle.scale;
      particleSprite.height = 4 * particle.scale;
      particleSprite.tint = particle.color;
      particleSprite.x = particle.x;
      particleSprite.y = particle.y;
      particleSprite.alpha = particle.life / particle.maxLife;
      particleContainer.addChild(particleSprite);
    });
  }

  function updateDebugVisualization() {
    if (!debugContainer) return;

    // Clear previous debug graphics
    debugContainer.removeChildren();

    if (!debugMode) return;

    // Draw monkey collision box
    const monkeyDebugBox = new PIXI.Graphics();
    monkeyDebugBox.stroke({ width: 2, color: 0xFF0000, alpha: 1 }); // Red border
    monkeyDebugBox.rect(monkey.x, monkey.y, 64, 64);
    debugContainer.addChild(monkeyDebugBox);

    // Draw platform collision boxes
    if (worldBuilder) {
      const allTiles = worldBuilder.getWorldTiles();
      for (const tile of allTiles) {
        if (tile.sprite) {
          const tileDebugBox = new PIXI.Graphics();
          tileDebugBox.stroke({ width: 1, color: 0x00FF00, alpha: 0.8 }); // Green border, semi-transparent
          tileDebugBox.rect(
            tile.sprite.x,
            tile.sprite.y,
            tile.sprite.width,
            tile.sprite.height
          );
          debugContainer.addChild(tileDebugBox);
        }
      }
    }

    // Draw banana collision boxes
    for (const banana of bananas) {
      if (banana.visible) {
        const bananaDebugBox = new PIXI.Graphics();
        bananaDebugBox.stroke({ width: 1, color: 0xFFFF00, alpha: 0.8 }); // Yellow border
        bananaDebugBox.rect(banana.x, banana.y, banana.width, banana.height);
        debugContainer.addChild(bananaDebugBox);
      }
    }

    // Draw camera dead zone (in screen coordinates)
    if (camera) {
      const deadZoneBox = new PIXI.Graphics();
      deadZoneBox.stroke({ width: 2, color: 0xFF00FF, alpha: 0.6 }); // Magenta border

      // Dead zone is centered on screen
      const deadZoneX = screenWidth / 2 - 40; // 80px width / 2
      const deadZoneY = screenHeight / 2 - 30; // 60px height / 2
      deadZoneBox.rect(deadZoneX, deadZoneY, 80, 60);
      debugContainer.addChild(deadZoneBox);

      // Draw camera center indicator
      const centerIndicator = new PIXI.Graphics();
      centerIndicator.circle(screenWidth / 2, screenHeight / 2, 3);
      centerIndicator.fill(0xFF00FF); // Magenta fill
      debugContainer.addChild(centerIndicator);

      // Draw monkey position text
      const positionText = new PIXI.Text({
        text: `Monkey: (${monkey.x.toFixed(0)}, ${monkey.y.toFixed(0)})`,
        style: {
          fontFamily: 'Arial',
          fontSize: 16,
          fill: 0xFFFFFF
        }
      });
      positionText.x = 10;
      positionText.y = 10;
      debugContainer.addChild(positionText);

      // Draw transition threshold indicator
      const currentScene = get(gameStore).currentScene;
      const sceneBounds = SCENE_BOUNDARIES[currentScene];
      if (sceneBounds) {
        const transitionThreshold = sceneBounds.right - 50;
        const thresholdText = new PIXI.Text({
          text: `Transition at X: ${transitionThreshold}`,
          style: {
            fontFamily: 'Arial',
            fontSize: 16,
            fill: 0x00FF00
          }
        });
        thresholdText.x = 10;
        thresholdText.y = 30;
        debugContainer.addChild(thresholdText);
      }
    }

    // Draw scene boundaries (in world coordinates, so they move with camera)
    const currentScene = get(gameStore).currentScene;
    const sceneBounds = SCENE_BOUNDARIES[currentScene];
    if (sceneBounds && worldContainer) {
      // Create boundary visualization in world container so it moves with camera
      const boundaryGraphics = new PIXI.Graphics();
      boundaryGraphics.stroke({ width: 3, color: 0x00FF00, alpha: 0.8 }); // Green border

      // Draw scene boundary rectangle
      boundaryGraphics.rect(
        sceneBounds.left,
        sceneBounds.top,
        sceneBounds.right - sceneBounds.left,
        sceneBounds.bottom - sceneBounds.top
      );

      // Add to world container so it moves with camera
      worldContainer.addChild(boundaryGraphics);

      // Draw transition zones
      for (const [, transition] of Object.entries(SCENE_TRANSITIONS)) {
        if (transition.scene !== currentScene) continue;

        const transitionGraphics = new PIXI.Graphics();
        transitionGraphics.stroke({ width: 2, color: 0xFFFF00, alpha: 0.7 }); // Yellow border

        // Draw transition zone based on boundary direction
        switch (transition.boundary) {
          case 'right':
            transitionGraphics.rect(
              sceneBounds.right - transition.threshold,
              sceneBounds.top,
              transition.threshold,
              sceneBounds.bottom - sceneBounds.top
            );
            break;
          case 'left':
            transitionGraphics.rect(
              sceneBounds.left,
              sceneBounds.top,
              transition.threshold,
              sceneBounds.bottom - sceneBounds.top
            );
            break;
          case 'top':
            transitionGraphics.rect(
              sceneBounds.left,
              sceneBounds.top,
              sceneBounds.right - sceneBounds.left,
              transition.threshold
            );
            break;
          case 'bottom':
            transitionGraphics.rect(
              sceneBounds.left,
              sceneBounds.bottom - transition.threshold,
              sceneBounds.right - sceneBounds.left,
              transition.threshold
            );
            break;
        }

        worldContainer.addChild(transitionGraphics);
      }
    }

    // Draw NPC interaction zones (in world coordinates)
    if (npcManager && worldContainer) {
      const npcContainer = npcManager.getContainer();

      // Draw interaction zones for all NPCs
      npcContainer.children.forEach((child: any) => {
        // Check if this is an interaction zone (they have a radius property)
        if (child.width && child.height && child.alpha === 0) {
          const interactionDebug = new PIXI.Graphics();
          interactionDebug.stroke({ width: 1, color: 0x00FFFF, alpha: 0.5 }); // Cyan border
          interactionDebug.circle(child.x, child.y, 50); // 50px radius interaction zone
          worldContainer.addChild(interactionDebug);
        }
      });
    }
  }

  function cleanup() {
    console.log('GameCanvas cleanup() called');

    // AGGRESSIVE CLEANUP: Remove all farmer-like sprites from stage
    if (pixiApp && pixiApp.stage) {
      const farmerSprites = pixiApp.stage.children.filter(child =>
        child instanceof PIXI.AnimatedSprite && child.zIndex === 90
      );

      if (farmerSprites.length > 0) {
        console.log(`Cleanup: Found ${farmerSprites.length} farmer sprites, removing them...`);
        farmerSprites.forEach(sprite => {
          if (sprite.parent) {
            pixiApp.stage.removeChild(sprite);
            sprite.destroy();
          }
        });
      }
    }

    // Clean up companions first
    if (companionManager) {
      companionManager.clearAll();
      companionManager = null;
    }

    // Clean up NPCs
    if (npcManager) {
      npcManager.clearAllNPCs();
      npcManager = null;
    }

    // Clean up background
    if (jungleBackground) {
      jungleBackground.destroy();
      jungleBackground = null;
    }

    if (pixiApp) {
      pixiApp.destroy(true);
    }

    // Remove event listeners (only if in browser)
    if (browser) {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    }

    gameInitialized = false;
  }
</script>

{#if browser}
  <div class="fullscreen-game-container">
    <div bind:this={canvasContainer} class="fullscreen-canvas" class:focused={canvasFocused} on:click={handleCanvasContainerClick} role="button" tabindex="0" on:keydown={(e) => e.key === 'Enter' && handleCanvasContainerClick()}></div>

    <!-- Welcome Toast Message -->
    {#if showToast}
      <div class="welcome-toast" class:visible={toastVisible} on:click={handleToastInteraction} role="button" tabindex="0" on:keydown={(e) => (e.key === 'Enter' || e.key === 'Escape') && handleToastInteraction()}>
        <div class="toast-content">
          <h3>🍌 Banana Checklist</h3>
          <p><strong>Controls:</strong> Arrow Keys or WASD to move, Space to jump</p>
          <p>Collect golden bananas to earn rewards!</p>
          <p class="toast-hint">💡 Click anywhere or press any key to start!</p>
        </div>
      </div>
    {/if}
  </div>
{:else}
  <div class="game-loading">
    <p>🐒 Loading jungle adventure...</p>
  </div>
{/if}

<!-- NPC Dialogue Interface -->
{#if showDialogue && currentNPC}
  <div class="dialogue-overlay" on:click={closeDialogue} role="button" tabindex="0" on:keydown={(e) => e.key === 'Escape' && closeDialogue()}>
    <div class="dialogue-box" on:click|stopPropagation on:keydown={(e) => e.key === 'Enter' && e.stopPropagation()} role="button" tabindex="0">
      <div class="dialogue-header">
        <h3>{currentNPC.name}</h3>
        <button class="close-btn" on:click={closeDialogue}>×</button>
      </div>

      {#each currentNPC.dialogues as dialogue}
        {#if dialogue.id === currentDialogueId}
          <div class="dialogue-content">
            <p class="dialogue-text">{dialogue.text}</p>

            {#if dialogue.responses}
              <div class="dialogue-responses">
                {#each dialogue.responses as response, index}
                  <button
                    class="response-btn"
                    on:click={() => handleDialogueResponse(index)}
                  >
                    {response.text}
                  </button>
                {/each}
              </div>
            {/if}
          </div>
        {/if}
      {/each}
    </div>
  </div>
{/if}

<style>
  .fullscreen-game-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .fullscreen-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
    display: block;
    z-index: 1;
  }

  .fullscreen-canvas.focused {
    outline: none;
  }

  .welcome-toast {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(-20px);
    background: rgba(255, 255, 255, 0.95);
    padding: 1.5rem 2rem;
    border-radius: 12px;
    text-align: center;
    max-width: 400px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border: 2px solid rgba(16, 185, 129, 0.3);
    z-index: 100;
    pointer-events: auto;
    opacity: 0;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(8px);
  }

  .welcome-toast.visible {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }

  .welcome-toast:not(.visible) {
    transform: translateX(-50%) translateY(-40px);
  }

  .toast-content h3 {
    margin: 0 0 1rem 0;
    color: #10B981;
    font-size: 1.4rem;
    font-family: 'Fredoka', sans-serif;
  }

  .toast-content p {
    margin: 0.5rem 0;
    font-size: 0.95rem;
    line-height: 1.4;
  }

  .toast-content strong {
    color: #10B981;
  }

  .toast-hint {
    color: #F59E0B !important;
    font-weight: 600;
    font-size: 0.9rem;
    animation: pulse 2s infinite;
    margin-top: 1rem !important;
  }

  .welcome-toast:hover {
    transform: translateX(-50%) translateY(-2px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
  }

  .game-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.95);
    padding: 2rem;
    border-radius: 16px;
    text-align: center;
    color: #6B7280;
    border: 2px dashed #D1D5DB;
    backdrop-filter: blur(10px);
  }

  /* Dialogue Interface Styles */
  .dialogue-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }

  .dialogue-box {
    background: linear-gradient(135deg, #FEF3C7 0%, #FDE68A 100%);
    border: 3px solid #D97706;
    border-radius: 16px;
    padding: 1.5rem;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    font-family: 'Fredoka', sans-serif;
  }

  .dialogue-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    border-bottom: 2px solid #D97706;
    padding-bottom: 0.5rem;
  }

  .dialogue-header h3 {
    margin: 0;
    color: #92400E;
    font-size: 1.5rem;
    font-weight: 600;
  }

  .close-btn {
    background: #DC2626;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    font-size: 1.2rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
  }

  .close-btn:hover {
    background: #B91C1C;
  }

  .dialogue-text {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #451A03;
    margin-bottom: 1.5rem;
  }

  .dialogue-responses {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .response-btn {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    font-family: 'Fredoka', sans-serif;
  }

  .response-btn:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  }
</style>
