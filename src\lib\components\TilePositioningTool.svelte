<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { browser } from '$app/environment';
  import * as PIXI from 'pixi.js';
  import { TilesetManager, WorldBuilder, JUNGLE_TILESET_CONFIG } from '$lib/utils/tileUtils';

  // Props
  export let visible = false;

  // Canvas element reference
  let canvasContainer: HTMLDivElement;
  let pixiApp: PIXI.Application;
  let toolInitialized = false;

  // Screen dimensions
  let screenWidth = 800;
  let screenHeight = 600;
  
  // Tool objects
  let tilesetManager: TilesetManager;
  let worldBuilder: WorldBuilder;
  let worldContainer: PIXI.Container;
  let gridContainer: PIXI.Container;
  let uiContainer: PIXI.Container;

  // Tool state
  let selectedTileId = 0;
  let gridSize = 32; // Default grid size (16px tiles * 2 scale)
  let showGrid = true;
  let placedTiles: Array<{id: number, x: number, y: number, scale: number}> = [];
  let availableTiles: number[] = [];

  // Jungle platform tile mappings (6x6 grid)
  const jungleTileMap = {
    // Platform structure tiles (outer edges)
    platform: {
      topLeft: 0,     // (0,0)
      topCenter: 1,   // (1,0)
      topRight: 5,    // (5,0)
      leftSide: 16,   // (0,1)
      rightSide: 21,  // (5,1)
      bottomLeft: 80, // (0,5)
      bottomCenter: 81, // (1,5)
      bottomRight: 85 // (5,5)
    },
    // Foliage tiles (inner area)
    foliage: {
      topLeft: 17,    // (1,1)
      topCenter: 18,  // (2,1)
      topRight: 20,   // (4,1)
      centerLeft: 33, // (1,2)
      center: 34,     // (2,2)
      centerRight: 36, // (4,2)
      bottomLeft: 65, // (1,4)
      bottomCenter: 66, // (2,4)
      bottomRight: 68 // (4,4)
    }
  };

  // Preset tile categories for easier selection
  let selectedCategory = 'platform';
  let selectedTileType = 'topLeft';

  // UI state
  let mouseX = 0;
  let mouseY = 0;
  let snappedX = 0;
  let snappedY = 0;

  // Drag state
  let isDragging = false;
  let draggedTileIndex = -1;
  let dragStartX = 0;
  let dragStartY = 0;
  let dragOffsetX = 0;
  let dragOffsetY = 0;

  $: if (visible && browser && !toolInitialized) {
    initializeTool();
  }

  $: if (!visible && toolInitialized) {
    cleanup();
  }

  onDestroy(() => {
    cleanup();
  });

  function updateScreenDimensions() {
    screenWidth = window.innerWidth;
    screenHeight = window.innerHeight - 120; // Account for header and tool UI
  }

  async function initializeTool() {
    if (toolInitialized) return;

    try {
      updateScreenDimensions();

      // Create PixiJS application
      pixiApp = new PIXI.Application();
      await pixiApp.init({
        width: screenWidth,
        height: screenHeight,
        backgroundColor: 0x2c3e50,
        antialias: true
      });

      // Add canvas to container
      canvasContainer.appendChild(pixiApp.canvas);

      // Enable z-index sorting
      pixiApp.stage.sortableChildren = true;

      // Create containers
      worldContainer = new PIXI.Container();
      worldContainer.sortableChildren = true;
      worldContainer.zIndex = 10;
      pixiApp.stage.addChild(worldContainer);

      gridContainer = new PIXI.Container();
      gridContainer.zIndex = 5;
      pixiApp.stage.addChild(gridContainer);

      uiContainer = new PIXI.Container();
      uiContainer.zIndex = 100;
      pixiApp.stage.addChild(uiContainer);

      // Initialize tileset manager
      tilesetManager = new TilesetManager(JUNGLE_TILESET_CONFIG);
      await tilesetManager.loadTileset('/assets/Tileset-Spritesheet.png');

      // Get available tiles
      availableTiles = tilesetManager.getAvailableTileIds();

      // Initialize world builder
      worldBuilder = new WorldBuilder(tilesetManager);
      const tileContainer = worldBuilder.getContainer();
      worldContainer.addChild(tileContainer);

      // Set up interaction
      setupInteraction();

      // Draw initial grid
      drawGrid();

      toolInitialized = true;
      console.log('Tile positioning tool initialized!');
    } catch (error) {
      console.error('Failed to initialize tile positioning tool:', error);
    }
  }

  function setupInteraction() {
    // Make stage interactive
    pixiApp.stage.eventMode = 'static';
    pixiApp.stage.hitArea = pixiApp.screen;

    // Mouse move for preview and dragging
    pixiApp.stage.on('pointermove', (event) => {
      const globalPos = event.global;
      mouseX = globalPos.x;
      mouseY = globalPos.y;

      // Snap to grid
      snappedX = Math.floor(mouseX / gridSize) * gridSize;
      snappedY = Math.floor(mouseY / gridSize) * gridSize;

      if (isDragging && draggedTileIndex >= 0) {
        // Update dragged tile position
        const newX = snappedX - dragOffsetX;
        const newY = snappedY - dragOffsetY;

        placedTiles[draggedTileIndex].x = newX;
        placedTiles[draggedTileIndex].y = newY;
        placedTiles = placedTiles; // Trigger reactivity

        // Update the visual tile position
        rebuildWorld();
      } else {
        updatePreview();
      }
    });

    // Mouse down - handle both placement and dragging
    pixiApp.stage.on('pointerdown', (event) => {
      const globalPos = event.global;
      const x = Math.floor(globalPos.x / gridSize) * gridSize;
      const y = Math.floor(globalPos.y / gridSize) * gridSize;

      if (event.button === 1) { // Middle mouse button
        // Check if clicking on an existing tile
        const tileIndex = findTileAtPosition(x, y);
        if (tileIndex >= 0) {
          startDragging(tileIndex, globalPos.x, globalPos.y);
        }
      } else if (event.button === 0) { // Left mouse button
        if (!isDragging) {
          const tileId = getSelectedTileId();
          placeTile(tileId, x, y, 2);
        }
      } else if (event.button === 2) { // Right mouse button
        // Delete tile at this position
        const tileIndex = findTileAtPosition(x, y);
        if (tileIndex >= 0) {
          deleteTile(tileIndex);
        }
      }
    });

    // Mouse up - stop dragging
    pixiApp.stage.on('pointerup', (event) => {
      if (event.button === 1 && isDragging) { // Middle mouse button
        stopDragging();
      }
    });

    // Handle mouse leave to stop dragging
    pixiApp.stage.on('pointerleave', () => {
      if (isDragging) {
        stopDragging();
      }
    });
  }

  function drawGrid() {
    if (!showGrid) return;

    gridContainer.removeChildren();

    const gridGraphics = new PIXI.Graphics();
    gridGraphics.stroke({ width: 1, color: 0x34495e, alpha: 0.5 });

    // Vertical lines
    for (let x = 0; x <= screenWidth; x += gridSize) {
      gridGraphics.moveTo(x, 0);
      gridGraphics.lineTo(x, screenHeight);
    }

    // Horizontal lines
    for (let y = 0; y <= screenHeight; y += gridSize) {
      gridGraphics.moveTo(0, y);
      gridGraphics.lineTo(screenWidth, y);
    }

    gridContainer.addChild(gridGraphics);
  }

  function updatePreview() {
    // Remove existing preview
    const existingPreview = uiContainer.children.find(child => child.label === 'preview');
    if (existingPreview) {
      uiContainer.removeChild(existingPreview);
    }

    // Don't show preview when dragging
    if (isDragging) {
      return;
    }

    // Create new preview
    const tileId = getSelectedTileId();
    const previewSprite = tilesetManager.createTileSprite(tileId, 2);
    if (previewSprite) {
      previewSprite.label = 'preview';
      previewSprite.x = snappedX;
      previewSprite.y = snappedY;
      previewSprite.alpha = 0.6;
      previewSprite.tint = 0x3498db; // Blue tint for preview
      uiContainer.addChild(previewSprite);
    }
  }

  function placeTile(tileId: number, x: number, y: number, scale: number) {
    const sprite = worldBuilder.addTile(tileId, x, y, scale);
    if (sprite) {
      placedTiles.push({ id: tileId, x, y, scale });
      placedTiles = placedTiles; // Trigger reactivity
      console.log(`Placed tile ${tileId} at (${x}, ${y})`);
    }
  }

  function findTileAtPosition(x: number, y: number): number {
    for (let i = placedTiles.length - 1; i >= 0; i--) {
      const tile = placedTiles[i];
      const tileSize = gridSize; // Assuming all tiles use the current grid size

      if (x >= tile.x && x < tile.x + tileSize &&
          y >= tile.y && y < tile.y + tileSize) {
        return i;
      }
    }
    return -1;
  }

  function startDragging(tileIndex: number, mouseX: number, mouseY: number) {
    isDragging = true;
    draggedTileIndex = tileIndex;
    dragStartX = mouseX;
    dragStartY = mouseY;

    const tile = placedTiles[tileIndex];
    dragOffsetX = mouseX - tile.x;
    dragOffsetY = mouseY - tile.y;

    console.log(`Started dragging tile ${tileIndex} at (${tile.x}, ${tile.y})`);
  }

  function stopDragging() {
    if (isDragging && draggedTileIndex >= 0) {
      console.log(`Stopped dragging tile ${draggedTileIndex} at (${placedTiles[draggedTileIndex].x}, ${placedTiles[draggedTileIndex].y})`);
    }

    isDragging = false;
    draggedTileIndex = -1;
    dragStartX = 0;
    dragStartY = 0;
    dragOffsetX = 0;
    dragOffsetY = 0;
  }

  function rebuildWorld() {
    // Clear the world and rebuild it with current tile positions
    worldBuilder.clearWorld();

    placedTiles.forEach((tile, index) => {
      const sprite = worldBuilder.addTile(tile.id, tile.x, tile.y, tile.scale);

      // Highlight the tile being dragged
      if (isDragging && index === draggedTileIndex && sprite) {
        sprite.tint = 0xffff00; // Yellow tint for dragged tile
        sprite.alpha = 0.8;
      }
    });
  }

  function getSelectedTileId(): number {
    if (selectedCategory === 'custom') {
      return selectedTileId;
    }

    const category = jungleTileMap[selectedCategory as keyof typeof jungleTileMap];
    if (category && selectedTileType in category) {
      return category[selectedTileType as keyof typeof category];
    }

    return selectedTileId;
  }

  function createJunglePlatform(startX: number, startY: number, width: number, height: number) {
    const tileSize = gridSize;

    for (let row = 0; row < height; row++) {
      for (let col = 0; col < width; col++) {
        const x = startX + (col * tileSize);
        const y = startY + (row * tileSize);

        let tileId: number;

        // Determine which tile to use based on position
        if (row === 0 && col === 0) {
          tileId = jungleTileMap.platform.topLeft;
        } else if (row === 0 && col === width - 1) {
          tileId = jungleTileMap.platform.topRight;
        } else if (row === height - 1 && col === 0) {
          tileId = jungleTileMap.platform.bottomLeft;
        } else if (row === height - 1 && col === width - 1) {
          tileId = jungleTileMap.platform.bottomRight;
        } else if (row === 0) {
          tileId = jungleTileMap.platform.topCenter;
        } else if (row === height - 1) {
          tileId = jungleTileMap.platform.bottomCenter;
        } else if (col === 0) {
          tileId = jungleTileMap.platform.leftSide;
        } else if (col === width - 1) {
          tileId = jungleTileMap.platform.rightSide;
        } else {
          // Interior tiles use foliage
          tileId = jungleTileMap.foliage.center;
        }

        placeTile(tileId, x, y, 2);
      }
    }
  }

  function deleteTile(tileIndex: number) {
    if (tileIndex >= 0 && tileIndex < placedTiles.length) {
      const deletedTile = placedTiles[tileIndex];
      placedTiles.splice(tileIndex, 1);
      placedTiles = placedTiles; // Trigger reactivity
      rebuildWorld();
      console.log(`Deleted tile ${deletedTile.id} at (${deletedTile.x}, ${deletedTile.y})`);
    }
  }

  function clearAllTiles() {
    worldBuilder.clearWorld();
    placedTiles = [];
  }

  function generateCode() {
    let code = '// Generated tile positioning code\n';
    code += '// Copy this into your createBasicWorld() or createFarmScene() function\n\n';
    
    placedTiles.forEach((tile, index) => {
      code += `worldBuilder.addTile(${tile.id}, ${tile.x}, ${tile.y}, ${tile.scale}); // Tile ${index + 1}\n`;
    });

    return code;
  }

  function exportCode() {
    const code = generateCode();
    navigator.clipboard.writeText(code).then(() => {
      alert('Code copied to clipboard!');
    }).catch(() => {
      // Fallback: show in alert
      alert('Copy this code:\n\n' + code);
    });
  }

  function cleanup() {
    if (pixiApp) {
      pixiApp.destroy(true);
      pixiApp = null as any;
    }
    toolInitialized = false;
  }

  // Reactive updates
  $: if (toolInitialized && showGrid !== undefined) {
    drawGrid();
  }

  $: if (toolInitialized && gridSize) {
    drawGrid();
  }
</script>

{#if visible}
<div class="tile-tool-overlay">
  <!-- Tool Controls -->
  <div class="tool-controls">
    <div class="control-group">
      <label>
        Tile Category:
        <select bind:value={selectedCategory}>
          <option value="platform">Platform Structure</option>
          <option value="foliage">Jungle Foliage</option>
          <option value="custom">Custom Tile ID</option>
        </select>
      </label>
    </div>

    {#if selectedCategory === 'platform'}
      <div class="control-group">
        <label>
          Platform Part:
          <select bind:value={selectedTileType}>
            <option value="topLeft">Top Left Corner</option>
            <option value="topCenter">Top Center</option>
            <option value="topRight">Top Right Corner</option>
            <option value="leftSide">Left Side</option>
            <option value="rightSide">Right Side</option>
            <option value="bottomLeft">Bottom Left Corner</option>
            <option value="bottomCenter">Bottom Center</option>
            <option value="bottomRight">Bottom Right Corner</option>
          </select>
        </label>
      </div>
    {:else if selectedCategory === 'foliage'}
      <div class="control-group">
        <label>
          Foliage Type:
          <select bind:value={selectedTileType}>
            <option value="topLeft">Top Left</option>
            <option value="topCenter">Top Center</option>
            <option value="topRight">Top Right</option>
            <option value="centerLeft">Center Left</option>
            <option value="center">Center</option>
            <option value="centerRight">Center Right</option>
            <option value="bottomLeft">Bottom Left</option>
            <option value="bottomCenter">Bottom Center</option>
            <option value="bottomRight">Bottom Right</option>
          </select>
        </label>
      </div>
    {:else}
      <div class="control-group">
        <label>
          Tile ID:
          <select bind:value={selectedTileId}>
            {#each availableTiles as tileId}
              <option value={tileId}>Tile {tileId}</option>
            {/each}
          </select>
        </label>
      </div>
    {/if}

    <div class="control-group">
      <label>
        Grid Size:
        <input type="range" min="16" max="64" step="16" bind:value={gridSize} />
        <span>{gridSize}px</span>
      </label>
    </div>

    <div class="control-group">
      <label>
        <input type="checkbox" bind:checked={showGrid} />
        Show Grid
      </label>
    </div>

    <div class="control-group">
      <button on:click={clearAllTiles}>Clear All</button>
      <button on:click={exportCode}>Export Code</button>
      <button on:click={() => createJunglePlatform(snappedX, snappedY, 4, 3)}>
        Create 4x3 Platform
      </button>
    </div>
  </div>

  <!-- Canvas Container -->
  <div class="canvas-container" bind:this={canvasContainer}></div>

  <!-- Status Info -->
  <div class="status-info">
    <div>Mouse: ({mouseX}, {mouseY})</div>
    <div>Snapped: ({snappedX}, {snappedY})</div>
    <div>Tiles Placed: {placedTiles.length}</div>
    <div>Selected Tile: {getSelectedTileId()} ({selectedCategory} - {selectedTileType})</div>
    {#if isDragging}
      <div class="drag-status">🖱️ Dragging tile {draggedTileIndex + 1}</div>
    {/if}
  </div>

  <!-- Instructions -->
  <div class="instructions-bar">
    <div class="instruction">🖱️ Left Click: Place tile</div>
    <div class="instruction">🖱️ Middle Click + Drag: Move tile</div>
    <div class="instruction">🖱️ Right Click: Delete tile</div>
  </div>
</div>
{/if}

<style>
  .tile-tool-overlay {
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    bottom: 0;
    background: #2c3e50;
    z-index: 1000;
    display: flex;
    flex-direction: column;
  }

  .tool-controls {
    background: #34495e;
    padding: 1rem;
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    align-items: center;
    color: white;
  }

  .control-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .control-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
  }

  .control-group select,
  .control-group input[type="range"] {
    padding: 0.25rem;
    border: 1px solid #7f8c8d;
    border-radius: 4px;
    background: #ecf0f1;
  }

  .control-group button {
    padding: 0.5rem 1rem;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
  }

  .control-group button:hover {
    background: #2980b9;
  }

  .canvas-container {
    flex: 1;
    position: relative;
    overflow: hidden;
  }

  .status-info {
    background: #34495e;
    padding: 0.5rem 1rem;
    display: flex;
    gap: 2rem;
    color: white;
    font-size: 0.8rem;
    font-family: monospace;
    align-items: center;
  }

  .drag-status {
    background: #e74c3c;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: bold;
    animation: pulse 1s infinite;
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
  }

  .instructions-bar {
    background: #2c3e50;
    padding: 0.5rem 1rem;
    display: flex;
    gap: 2rem;
    color: #bdc3c7;
    font-size: 0.75rem;
    border-top: 1px solid #34495e;
  }

  .instruction {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
</style>
