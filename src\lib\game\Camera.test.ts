import { describe, it, expect, beforeEach } from 'vitest';
import * as PIXI from 'pixi.js';
import { Camera, type CameraConfig } from './Camera';

describe('Camera', () => {
    let camera: Camera;
    let worldContainer: PIXI.Container;
    let config: CameraConfig;

    beforeEach(() => {
        worldContainer = new PIXI.Container();
        config = {
            smoothingFactor: 0.1,
            lookAheadDistance: 100,
            deadZoneWidth: 80,
            deadZoneHeight: 60,
            worldWidth: 1600,
            worldHeight: 900,
            viewportWidth: 800,
            viewportHeight: 600
        };
        camera = new Camera(worldContainer, config);
    });

    it('should initialize with correct default position', () => {
        const position = camera.getPosition();
        expect(position.x).toBe(400); // viewportWidth / 2
        expect(position.y).toBe(300); // viewportHeight / 2
    });

    it('should set position correctly', () => {
        camera.setPosition(100, 200);
        const position = camera.getPosition();
        expect(position.x).toBe(100);
        expect(position.y).toBe(200);
    });

    it('should update target position outside dead zone', () => {
        // Move target far enough outside dead zone to trigger update
        camera.setTarget({ x: 600, y: 500 }); // Far outside 80x60 dead zone from center (400,300)
        const target = camera.getTarget();
        expect(target.x).toBe(560); // 600 - deadZoneWidth/2 = 600 - 40 = 560
        expect(target.y).toBe(470); // 500 - deadZoneHeight/2 = 500 - 30 = 470
    });

    it('should apply look-ahead based on velocity', () => {
        camera.setTarget({ x: 500, y: 400, vx: 10, vy: 0 });
        const target = camera.getTarget();
        // Target should be offset by look-ahead distance in the direction of movement
        expect(target.x).toBeGreaterThan(500);
    });

    it('should respect dead zone', () => {
        // Set initial position
        camera.setPosition(500, 400);

        // Move target within dead zone - should not update target
        camera.setTarget({ x: 520, y: 410 }); // Within 80x60 dead zone
        const target = camera.getTarget();
        expect(target.x).toBe(500); // Should remain at original position
        expect(target.y).toBe(400);
    });

    it('should clamp to world boundaries', () => {
        // Try to set position outside world bounds
        camera.setPosition(2000, 1000); // Beyond worldWidth/Height
        camera.update(1); // Force update to apply clamping

        const position = camera.getPosition();
        expect(position.x).toBeLessThanOrEqual(config.worldWidth - config.viewportWidth / 2);
        expect(position.y).toBeLessThanOrEqual(config.worldHeight - config.viewportHeight / 2);
    });

    it('should update container position correctly', () => {
        camera.setPosition(500, 400);
        camera.update(1);

        // Container should be positioned opposite to camera
        expect(worldContainer.x).toBe(config.viewportWidth / 2 - 500);
        expect(worldContainer.y).toBe(config.viewportHeight / 2 - 400);
    });

    it('should snap to target immediately', () => {
        camera.setTarget({ x: 600, y: 500 });
        camera.snapToTarget();

        const position = camera.getPosition();
        const target = camera.getTarget();
        expect(position.x).toBe(target.x);
        expect(position.y).toBe(target.y);
    });

    it('should update configuration', () => {
        camera.updateConfig({ viewportWidth: 1000, viewportHeight: 800 });
        // This test mainly ensures the method doesn't throw errors
        expect(camera).toBeDefined();
    });
});
