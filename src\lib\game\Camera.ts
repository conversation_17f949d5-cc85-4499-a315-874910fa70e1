import * as PIXI from 'pixi.js';

export interface CameraTarget {
  x: number;
  y: number;
  vx?: number; // velocity for look-ahead
  vy?: number;
}

export interface CameraConfig {
  smoothingFactor: number; // 0.1 = smooth, 1.0 = instant
  lookAheadDistance: number; // pixels to look ahead based on velocity
  deadZoneWidth: number; // width of dead zone around target
  deadZoneHeight: number; // height of dead zone around target
  worldWidth: number; // total world width for boundary clamping
  worldHeight: number; // total world height for boundary clamping
  viewportWidth: number; // camera viewport width
  viewportHeight: number; // camera viewport height
}

export class Camera {
  private x: number = 0;
  private y: number = 0;
  private targetX: number = 0;
  private targetY: number = 0;
  private config: CameraConfig;
  private worldContainer: PIXI.Container;
  private deadZoneCenterX: number = 0;
  private deadZoneCenterY: number = 0;

  constructor(worldContainer: PIXI.Container, config: CameraConfig) {
    this.worldContainer = worldContainer;
    this.config = config;
    
    // Initialize camera position to center of viewport
    this.x = config.viewportWidth / 2;
    this.y = config.viewportHeight / 2;
    this.targetX = this.x;
    this.targetY = this.y;
    this.deadZoneCenterX = this.x;
    this.deadZoneCenterY = this.y;
  }

  /**
   * Update camera configuration (useful for screen resize)
   */
  updateConfig(config: Partial<CameraConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Set the target for the camera to follow
   */
  setTarget(target: CameraTarget): void {
    // Calculate look-ahead offset based on velocity
    let lookAheadX = 0;
    let lookAheadY = 0;
    
    if (target.vx !== undefined && target.vy !== undefined) {
      // Normalize velocity and apply look-ahead distance
      const speed = Math.sqrt(target.vx * target.vx + target.vy * target.vy);
      if (speed > 0.1) { // Only apply look-ahead if moving
        lookAheadX = (target.vx / speed) * this.config.lookAheadDistance;
        lookAheadY = (target.vy / speed) * this.config.lookAheadDistance;
      }
    }

    // Calculate potential new target position with look-ahead
    const potentialTargetX = target.x + lookAheadX;
    const potentialTargetY = target.y + lookAheadY;

    // Apply dead zone logic
    const deadZoneLeft = this.deadZoneCenterX - this.config.deadZoneWidth / 2;
    const deadZoneRight = this.deadZoneCenterX + this.config.deadZoneWidth / 2;
    const deadZoneTop = this.deadZoneCenterY - this.config.deadZoneHeight / 2;
    const deadZoneBottom = this.deadZoneCenterY + this.config.deadZoneHeight / 2;

    // Only update target if outside dead zone
    if (potentialTargetX < deadZoneLeft) {
      this.targetX = potentialTargetX + this.config.deadZoneWidth / 2;
      this.deadZoneCenterX = this.targetX;
    } else if (potentialTargetX > deadZoneRight) {
      this.targetX = potentialTargetX - this.config.deadZoneWidth / 2;
      this.deadZoneCenterX = this.targetX;
    }

    if (potentialTargetY < deadZoneTop) {
      this.targetY = potentialTargetY + this.config.deadZoneHeight / 2;
      this.deadZoneCenterY = this.targetY;
    } else if (potentialTargetY > deadZoneBottom) {
      this.targetY = potentialTargetY - this.config.deadZoneHeight / 2;
      this.deadZoneCenterY = this.targetY;
    }
  }

  /**
   * Update camera position with smooth interpolation
   */
  update(deltaTime: number): void {
    // Apply smooth interpolation
    const lerpFactor = this.config.smoothingFactor * deltaTime;
    this.x += (this.targetX - this.x) * lerpFactor;
    this.y += (this.targetY - this.y) * lerpFactor;

    // Clamp camera to world boundaries
    this.clampToWorldBounds();

    // Apply camera position to world container
    this.applyTransform();
  }

  /**
   * Clamp camera position to world boundaries
   */
  private clampToWorldBounds(): void {
    const halfViewportWidth = this.config.viewportWidth / 2;
    const halfViewportHeight = this.config.viewportHeight / 2;

    // Clamp X position
    this.x = Math.max(
      halfViewportWidth,
      Math.min(this.x, this.config.worldWidth - halfViewportWidth)
    );

    // Clamp Y position
    this.y = Math.max(
      halfViewportHeight,
      Math.min(this.y, this.config.worldHeight - halfViewportHeight)
    );
  }

  /**
   * Apply camera transform to world container using offset method
   */
  private applyTransform(): void {
    // Use offset method: move world container opposite to camera position
    this.worldContainer.x = this.config.viewportWidth / 2 - this.x;
    this.worldContainer.y = this.config.viewportHeight / 2 - this.y;
  }

  /**
   * Get current camera position
   */
  getPosition(): { x: number; y: number } {
    return { x: this.x, y: this.y };
  }

  /**
   * Get current target position
   */
  getTarget(): { x: number; y: number } {
    return { x: this.targetX, y: this.targetY };
  }

  /**
   * Immediately snap camera to target (no interpolation)
   */
  snapToTarget(): void {
    this.x = this.targetX;
    this.y = this.targetY;
    this.deadZoneCenterX = this.x;
    this.deadZoneCenterY = this.y;
    this.clampToWorldBounds();
    this.applyTransform();
  }

  /**
   * Set camera position directly (useful for initialization)
   */
  setPosition(x: number, y: number): void {
    this.x = x;
    this.y = y;
    this.targetX = x;
    this.targetY = y;
    this.deadZoneCenterX = x;
    this.deadZoneCenterY = y;
    this.applyTransform();
  }
}
