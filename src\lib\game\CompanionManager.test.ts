import { describe, it, expect, beforeEach, vi } from 'vitest';
import { CompanionManager, BaseCompanion } from './CompanionManager';
import type { CompanionConfig } from '../types';

// Mock PIXI
vi.mock('pixi.js', () => ({
    Application: vi.fn(() => ({
        stage: {
            addChild: vi.fn(),
            removeChild: vi.fn(),
            sortableChildren: true
        },
        screen: {
            width: 800,
            height: 600
        }
    })),
    AnimatedSprite: vi.fn(() => ({
        anchor: { set: vi.fn() },
        scale: { set: vi.fn(), x: 1, y: 1 },
        x: 0,
        y: 0,
        animationSpeed: 0.1,
        loop: true,
        play: vi.fn(),
        stop: vi.fn(),
        zIndex: 90,
        parent: null
    })),
    Texture: {
        WHITE: {}
    },
    Assets: {
        load: vi.fn().mockResolvedValue({
            width: 768,
            height: 64,
            source: { width: 768, height: 64 }
        })
    },
    Rectangle: vi.fn()
}));

// Mock companion for testing
class TestCompanion extends BaseCompanion {
    constructor(app: any, config: CompanionConfig) {
        super(app, config);
    }

    protected async loadSprites(): Promise<void> {
        // Mock sprite loading
        this.frames.idle = [{}] as any;
        this.frames.walking = [{}] as any;
    }
}

const mockApp = {
    stage: {
        addChild: vi.fn(),
        removeChild: vi.fn(),
        sortableChildren: true
    },
    screen: {
        width: 800,
        height: 600
    }
} as any;

const testConfig: CompanionConfig = {
    type: 'farmer',
    name: 'Test Farmer',
    spriteSheet: '/test/farmer.png',
    frameCount: 24,
    frameSize: { width: 32, height: 64 },
    scale: 4,
    animationSpeed: 0.1,
    followDistance: 80,
    moveSpeed: 3
};

describe('CompanionManager', () => {
    let companionManager: CompanionManager;

    beforeEach(() => {
        vi.clearAllMocks();
        companionManager = new CompanionManager(mockApp);
    });

    describe('addCompanion', () => {
        it('should add a companion to the manager', () => {
            const companion = new TestCompanion(mockApp, testConfig);

            companionManager.addCompanion(companion, 'farmer');

            expect(companionManager['companions'].has('farmer')).toBe(true);
        });
    });

    describe('setActiveCompanion', () => {
        it('should set the active companion', async () => {
            const companion = new TestCompanion(mockApp, testConfig);
            companionManager.addCompanion(companion, 'farmer');

            await companionManager.setActiveCompanion('farmer');

            expect(companionManager.getActiveCompanion()).toBe(companion);
        });

        it('should destroy previous active companion when setting new one', async () => {
            const companion1 = new TestCompanion(mockApp, testConfig);
            const companion2 = new TestCompanion(mockApp, { ...testConfig, type: 'wizard' });

            companionManager.addCompanion(companion1, 'farmer');
            companionManager.addCompanion(companion2, 'wizard');

            await companionManager.setActiveCompanion('farmer');
            const destroySpy = vi.spyOn(companion1, 'destroy');

            await companionManager.setActiveCompanion('wizard');

            expect(destroySpy).toHaveBeenCalled();
            expect(companionManager.getActiveCompanion()).toBe(companion2);
        });
    });

    describe('update', () => {
        it('should update the active companion', async () => {
            const companion = new TestCompanion(mockApp, testConfig);
            const updateSpy = vi.spyOn(companion, 'update');

            companionManager.addCompanion(companion, 'farmer');
            await companionManager.setActiveCompanion('farmer');

            const targetPosition = { x: 100, y: 200 };
            companionManager.update(16, targetPosition);

            expect(updateSpy).toHaveBeenCalledWith(16, targetPosition);
        });

        it('should not update if no active companion', () => {
            const targetPosition = { x: 100, y: 200 };

            // Should not throw error
            expect(() => {
                companionManager.update(16, targetPosition);
            }).not.toThrow();
        });
    });

    describe('clearAll', () => {
        it('should destroy all companions and clear the map', async () => {
            const companion1 = new TestCompanion(mockApp, testConfig);
            const companion2 = new TestCompanion(mockApp, { ...testConfig, type: 'wizard' });

            const destroySpy1 = vi.spyOn(companion1, 'destroy');
            const destroySpy2 = vi.spyOn(companion2, 'destroy');

            companionManager.addCompanion(companion1, 'farmer');
            companionManager.addCompanion(companion2, 'wizard');
            await companionManager.setActiveCompanion('farmer');

            companionManager.clearAll();

            expect(destroySpy1).toHaveBeenCalled();
            expect(destroySpy2).toHaveBeenCalled();
            expect(companionManager['companions'].size).toBe(0);
            expect(companionManager.getActiveCompanion()).toBeNull();
        });
    });
});

describe('BaseCompanion', () => {
    let companion: TestCompanion;

    beforeEach(() => {
        vi.clearAllMocks();
        companion = new TestCompanion(mockApp, testConfig);
    });

    describe('initialization', () => {
        it('should initialize with correct config', () => {
            expect(companion['config']).toBe(testConfig);
            expect(companion['position']).toEqual({ x: 0, y: 0 });
            expect(companion['velocity']).toEqual({ x: 0, y: 0 });
            expect(companion['currentAnimation']).toBe('idle');
        });

        it('should initialize sprites on initialize call', async () => {
            await companion.initialize();

            expect(companion['isInitialized']).toBe(true);
            expect(mockApp.stage.addChild).toHaveBeenCalled();
        });
    });

    describe('movement', () => {
        beforeEach(async () => {
            await companion.initialize();
        });

        it('should move towards target when far enough', () => {
            const targetPosition = { x: 200, y: 200 };
            companion.setPosition(0, 0);

            companion.update(16, targetPosition);

            const position = companion.getPosition();
            expect(position.x).toBeGreaterThan(0);
            expect(position.y).toBeGreaterThan(0);
        });

        it('should not move when close to target', () => {
            const targetPosition = { x: 50, y: 50 };
            companion.setPosition(50, 50);

            companion.update(16, targetPosition);

            const position = companion.getPosition();
            expect(position.x).toBe(50);
            expect(position.y).toBe(50);
        });

        it('should stay within screen bounds', () => {
            const targetPosition = { x: -100, y: -100 };
            companion.setPosition(0, 0);

            companion.update(16, targetPosition);

            const position = companion.getPosition();
            expect(position.x).toBeGreaterThanOrEqual(0);
            expect(position.y).toBeGreaterThanOrEqual(0);
        });
    });

    describe('animation', () => {
        beforeEach(async () => {
            await companion.initialize();
        });

        it('should set animation state', () => {
            companion.setAnimation('walking');
            expect(companion['currentAnimation']).toBe('walking');
        });

        it('should not change animation if already in that state', () => {
            const removeChildSpy = vi.spyOn(mockApp.stage, 'removeChild');

            // First call should change animation
            companion.setAnimation('walking');
            const firstCallCount = removeChildSpy.mock.calls.length;

            // Second call with same animation should not change
            companion.setAnimation('walking');
            const secondCallCount = removeChildSpy.mock.calls.length;

            expect(secondCallCount).toBe(firstCallCount);
        });
    });

    describe('position management', () => {
        it('should set and get position correctly', () => {
            companion.setPosition(100, 200);

            const position = companion.getPosition();
            expect(position).toEqual({ x: 100, y: 200 });
        });
    });

    describe('destroy', () => {
        it('should remove sprite from stage and mark as not initialized', async () => {
            await companion.initialize();

            // Mock the sprite to have a parent (simulating being added to stage)
            companion['sprite'].parent = mockApp.stage;

            const removeChildSpy = vi.spyOn(mockApp.stage, 'removeChild');

            companion.destroy();

            expect(removeChildSpy).toHaveBeenCalled();
            expect(companion['isInitialized']).toBe(false);
        });
    });
});
