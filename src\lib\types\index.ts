// Banana Checklist - Type definitions

export interface Task {
    id: string;
    title: string;
    description?: string;
    completed: boolean;
    priority: 'low' | 'medium' | 'high';
    category?: string;
    dueDate?: Date;
    createdAt: Date;
    completedAt?: Date;
    bananaReward: number;
}

export interface Goal {
    id: string;
    title: string;
    description?: string;
    milestones: Milestone[];
    category?: string;
    targetDate?: Date;
    createdAt: Date;
    completedAt?: Date;
    totalBananaReward: number;
}

export interface Milestone {
    id: string;
    title: string;
    completed: boolean;
    bananaReward: number;
    completedAt?: Date;
}

export interface User {
    id: string;
    bananaCount: number;
    totalTasksCompleted: number;
    totalGoalsCompleted: number;
    unlockedFeatures: string[];
    isPremium: boolean;
    createdAt: Date;
    lastActiveAt: Date;
}

export interface GameState {
    monkeyPosition: { x: number; y: number };
    currentScene: 'jungle' | 'grove' | 'minigame' | 'banana-farm';
    unlockedAreas: string[];
    farmData?: FarmState;
    cosmetics: {
        monkeySkin: string;
        hat?: string;
        theme: string;
    };
    upgrades: GameUpgrade[];
    companion?: CompanionState;
}

export interface GameUpgrade {
    id: string;
    name: string;
    description: string;
    cost: number;
    purchased: boolean;
    effect: string;
    level?: number; // For tiered upgrades like banana bots
    maxLevel?: number;
}

// Farm-specific state management
export interface FarmState {
    botLevel: number;
    plantationLevel: number;
    generationRate: number; // bananas per minute
    lastUpdateTime: number; // timestamp for offline calculation
    totalGenerated: number;
    isUnlocked: boolean;
}

// NPC system types
export interface NPC {
    id: string;
    name: string;
    type: 'vendor' | 'quest-giver' | 'decorator';
    position: { x: number; y: number };
    spriteSheet: string;
    dialogues: NPCDialogue[];
    shop?: NPCShop;
}

export interface NPCDialogue {
    id: string;
    text: string;
    responses?: NPCResponse[];
    condition?: string; // Optional condition for showing dialogue
}

export interface NPCResponse {
    text: string;
    action: 'shop' | 'quest' | 'close' | 'dialogue';
    target?: string; // Target dialogue ID or action
}

export interface NPCShop {
    items: ShopItem[];
    currency: 'bananas' | 'coins';
}

export interface ShopItem {
    id: string;
    name: string;
    description: string;
    cost: number;
    effect: string;
    maxLevel: number;
    currentLevel: number;
    category: 'bot' | 'plantation' | 'cosmetic';
}

export interface Quest {
    id: string;
    title: string;
    description: string;
    type: 'daily' | 'weekly' | 'achievement';
    requirements: QuestRequirement[];
    bananaReward: number;
    completed: boolean;
    progress: number;
    maxProgress: number;
}

export interface QuestRequirement {
    type: 'complete_tasks' | 'maintain_streak' | 'collect_bananas';
    target: number;
    current: number;
}

export interface AppSettings {
    soundEnabled: boolean;
    musicEnabled: boolean;
    retroFilter: boolean;
    highContrast: boolean;
    animationsEnabled: boolean;
}

// Game-specific types
export interface Sprite {
    x: number;
    y: number;
    width: number;
    height: number;
    texture: string;
    visible: boolean;
}

export interface GameInput {
    left: boolean;
    right: boolean;
    up: boolean;
    down: boolean;
    jump: boolean;
    interact: boolean;
}

// Companion system types
export interface CompanionState {
    type: CompanionType;
    position: { x: number; y: number };
    isActive: boolean;
    isFollowing: boolean;
    animationState: 'idle' | 'walking' | 'running';
}

export type CompanionType = 'farmer' | 'wizard' | 'knight' | 'archer';

export interface CompanionConfig {
    type: CompanionType;
    name: string;
    spriteSheet: string;
    frameCount: number;
    frameSize: { width: number; height: number };
    scale: number;
    animationSpeed: number;
    followDistance: number;
    moveSpeed: number;
}

export interface CompanionBehavior {
    update(deltaTime: number, targetPosition: { x: number; y: number }): void;
    setAnimation(state: 'idle' | 'walking' | 'running'): void;
    getPosition(): { x: number; y: number };
    setPosition(x: number, y: number): void;
    destroy(): void;
}

// API types
export interface ApiResponse<T> {
    success: boolean;
    data?: T;
    error?: string;
}

export interface TaskCreateRequest {
    title: string;
    description?: string;
    priority: 'low' | 'medium' | 'high';
    category?: string;
    dueDate?: string;
}

export interface TaskUpdateRequest {
    title?: string;
    description?: string;
    priority?: 'low' | 'medium' | 'high';
    category?: string;
    dueDate?: string;
    completed?: boolean;
}
