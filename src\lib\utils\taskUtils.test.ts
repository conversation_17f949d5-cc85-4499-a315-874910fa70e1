import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
    calculateTaskBananas,
    calculateGoalBananas,
    formatDueDate,
    isTaskOverdue,
    getTaskPriorityColor,
    sortTasks,
    filterTasksByCategory,
    getTaskCategories,
    calculateGoalProgress
} from './taskUtils';
import type { Task, Goal, Milestone } from '../types';

describe('taskUtils', () => {
    describe('calculateTaskBananas', () => {
        it('should calculate correct bananas for low priority task', () => {
            expect(calculateTaskBananas('low')).toBe(5);
        });

        it('should calculate correct bananas for medium priority task', () => {
            expect(calculateTaskBananas('medium')).toBe(10);
        });

        it('should calculate correct bananas for high priority task', () => {
            expect(calculateTaskBananas('high')).toBe(15);
        });

        it('should add bonus for having description', () => {
            expect(calculateTaskBananas('low', true)).toBe(7); // 5 + 2
            expect(calculateTaskBananas('medium', true)).toBe(12); // 10 + 2
            expect(calculateTaskBananas('high', true)).toBe(17); // 15 + 2
        });

        it('should not add bonus when hasDescription is false', () => {
            expect(calculateTaskBananas('low', false)).toBe(5);
            expect(calculateTaskBananas('medium', false)).toBe(10);
            expect(calculateTaskBananas('high', false)).toBe(15);
        });
    });

    describe('calculateGoalBananas', () => {
        it('should calculate bananas for small goals', () => {
            expect(calculateGoalBananas(1)).toBe(25); // 25 * 1 + 0 complexity bonus
            expect(calculateGoalBananas(2)).toBe(50); // 25 * 2 + 0 complexity bonus
        });

        it('should add complexity bonus for larger goals', () => {
            expect(calculateGoalBananas(3)).toBe(85); // 25 * 3 + 10 complexity bonus
            expect(calculateGoalBananas(6)).toBe(170); // 25 * 6 + 20 complexity bonus
            expect(calculateGoalBananas(9)).toBe(255); // 25 * 9 + 30 complexity bonus
        });

        it('should handle zero milestones', () => {
            expect(calculateGoalBananas(0)).toBe(0);
        });
    });

    describe('formatDueDate', () => {
        beforeEach(() => {
            // Mock current date to January 1, 2024
            vi.useFakeTimers();
            vi.setSystemTime(new Date('2024-01-01T12:00:00Z'));
        });

        afterEach(() => {
            vi.useRealTimers();
        });

        it('should format overdue dates', () => {
            const yesterday = new Date('2023-12-31T12:00:00Z');
            expect(formatDueDate(yesterday)).toBe('Overdue');
        });

        it('should format due today', () => {
            const today = new Date('2024-01-01T12:00:00Z');
            expect(formatDueDate(today)).toBe('Due today');
        });

        it('should format due tomorrow', () => {
            const tomorrow = new Date('2024-01-02T12:00:00Z');
            expect(formatDueDate(tomorrow)).toBe('Due tomorrow');
        });

        it('should format due in X days for near future', () => {
            const threeDays = new Date('2024-01-04T12:00:00Z');
            expect(formatDueDate(threeDays)).toBe('Due in 3 days');

            const sevenDays = new Date('2024-01-08T12:00:00Z');
            expect(formatDueDate(sevenDays)).toBe('Due in 7 days');
        });

        it('should format far future dates as locale string', () => {
            const farFuture = new Date('2024-01-15T12:00:00Z');
            expect(formatDueDate(farFuture)).toBe(farFuture.toLocaleDateString());
        });
    });

    describe('isTaskOverdue', () => {
        beforeEach(() => {
            vi.useFakeTimers();
            vi.setSystemTime(new Date('2024-01-01T12:00:00Z'));
        });

        afterEach(() => {
            vi.useRealTimers();
        });

        it('should return false for tasks without due date', () => {
            const task: Task = {
                id: '1',
                title: 'Test Task',
                completed: false,
                priority: 'medium',
                createdAt: new Date(),
                bananaReward: 10
            };
            expect(isTaskOverdue(task)).toBe(false);
        });

        it('should return false for completed tasks', () => {
            const task: Task = {
                id: '1',
                title: 'Test Task',
                completed: true,
                priority: 'medium',
                createdAt: new Date(),
                bananaReward: 10,
                dueDate: new Date('2023-12-31T12:00:00Z') // Yesterday
            };
            expect(isTaskOverdue(task)).toBe(false);
        });

        it('should return true for overdue incomplete tasks', () => {
            const task: Task = {
                id: '1',
                title: 'Test Task',
                completed: false,
                priority: 'medium',
                createdAt: new Date(),
                bananaReward: 10,
                dueDate: new Date('2023-12-31T12:00:00Z') // Yesterday
            };
            expect(isTaskOverdue(task)).toBe(true);
        });

        it('should return false for future due dates', () => {
            const task: Task = {
                id: '1',
                title: 'Test Task',
                completed: false,
                priority: 'medium',
                createdAt: new Date(),
                bananaReward: 10,
                dueDate: new Date('2024-01-02T12:00:00Z') // Tomorrow
            };
            expect(isTaskOverdue(task)).toBe(false);
        });
    });

    describe('getTaskPriorityColor', () => {
        it('should return correct colors for each priority', () => {
            expect(getTaskPriorityColor('low')).toBe('#10B981');
            expect(getTaskPriorityColor('medium')).toBe('#F59E0B');
            expect(getTaskPriorityColor('high')).toBe('#EF4444');
        });
    });

    describe('sortTasks', () => {
        const createTask = (id: string, priority: Task['priority'], completed: boolean, dueDate?: Date): Task => ({
            id,
            title: `Task ${id}`,
            completed,
            priority,
            createdAt: new Date(),
            bananaReward: 10,
            dueDate
        });

        it('should sort incomplete tasks before completed tasks', () => {
            const tasks = [
                createTask('1', 'medium', true),
                createTask('2', 'medium', false),
                createTask('3', 'medium', true)
            ];

            const sorted = sortTasks(tasks);
            expect(sorted[0].completed).toBe(false);
            expect(sorted[1].completed).toBe(true);
            expect(sorted[2].completed).toBe(true);
        });

        it('should sort by priority within same completion status', () => {
            const tasks = [
                createTask('1', 'low', false),
                createTask('2', 'high', false),
                createTask('3', 'medium', false)
            ];

            const sorted = sortTasks(tasks);
            expect(sorted[0].priority).toBe('high');
            expect(sorted[1].priority).toBe('medium');
            expect(sorted[2].priority).toBe('low');
        });

        it('should sort by due date within same priority', () => {
            const tasks = [
                createTask('1', 'medium', false, new Date('2024-01-03T12:00:00Z')),
                createTask('2', 'medium', false, new Date('2024-01-01T12:00:00Z')),
                createTask('3', 'medium', false, new Date('2024-01-02T12:00:00Z'))
            ];

            const sorted = sortTasks(tasks);
            expect(sorted[0].dueDate?.getTime()).toBe(new Date('2024-01-01T12:00:00Z').getTime());
            expect(sorted[1].dueDate?.getTime()).toBe(new Date('2024-01-02T12:00:00Z').getTime());
            expect(sorted[2].dueDate?.getTime()).toBe(new Date('2024-01-03T12:00:00Z').getTime());
        });

        it('should prioritize tasks with due dates over those without', () => {
            const tasks = [
                createTask('1', 'medium', false),
                createTask('2', 'medium', false, new Date('2024-01-01'))
            ];

            const sorted = sortTasks(tasks);
            expect(sorted[0].dueDate).toBeDefined();
            expect(sorted[1].dueDate).toBeUndefined();
        });

        it('should not mutate original array', () => {
            const tasks = [
                createTask('1', 'low', false),
                createTask('2', 'high', false)
            ];
            const originalOrder = tasks.map(t => t.id);

            sortTasks(tasks);
            expect(tasks.map(t => t.id)).toEqual(originalOrder);
        });
    });

    describe('filterTasksByCategory', () => {
        const tasks: Task[] = [
            {
                id: '1',
                title: 'Work Task',
                completed: false,
                priority: 'medium',
                category: 'Work',
                createdAt: new Date(),
                bananaReward: 10
            },
            {
                id: '2',
                title: 'Personal Task',
                completed: false,
                priority: 'medium',
                category: 'Personal',
                createdAt: new Date(),
                bananaReward: 10
            },
            {
                id: '3',
                title: 'Another Work Task',
                completed: false,
                priority: 'medium',
                category: 'Work',
                createdAt: new Date(),
                bananaReward: 10
            }
        ];

        it('should return all tasks when category is "all"', () => {
            const filtered = filterTasksByCategory(tasks, 'all');
            expect(filtered).toHaveLength(3);
        });

        it('should filter tasks by specific category', () => {
            const workTasks = filterTasksByCategory(tasks, 'Work');
            expect(workTasks).toHaveLength(2);
            expect(workTasks.every(task => task.category === 'Work')).toBe(true);

            const personalTasks = filterTasksByCategory(tasks, 'Personal');
            expect(personalTasks).toHaveLength(1);
            expect(personalTasks[0].category).toBe('Personal');
        });

        it('should return empty array for non-existent category', () => {
            const filtered = filterTasksByCategory(tasks, 'NonExistent');
            expect(filtered).toHaveLength(0);
        });
    });

    describe('getTaskCategories', () => {
        it('should extract unique categories from tasks', () => {
            const tasks: Task[] = [
                { id: '1', title: 'Task 1', completed: false, priority: 'medium', category: 'Work', createdAt: new Date(), bananaReward: 10 },
                { id: '2', title: 'Task 2', completed: false, priority: 'medium', category: 'Personal', createdAt: new Date(), bananaReward: 10 },
                { id: '3', title: 'Task 3', completed: false, priority: 'medium', category: 'Work', createdAt: new Date(), bananaReward: 10 }
            ];

            const categories = getTaskCategories(tasks);
            expect(categories).toEqual(['Personal', 'Work']); // Sorted alphabetically
        });

        it('should handle tasks without categories', () => {
            const tasks: Task[] = [
                { id: '1', title: 'Task 1', completed: false, priority: 'medium', createdAt: new Date(), bananaReward: 10 },
                { id: '2', title: 'Task 2', completed: false, priority: 'medium', category: 'Work', createdAt: new Date(), bananaReward: 10 }
            ];

            const categories = getTaskCategories(tasks);
            expect(categories).toEqual(['Uncategorized', 'Work']);
        });

        it('should return empty array for empty task list', () => {
            const categories = getTaskCategories([]);
            expect(categories).toEqual([]);
        });
    });

    describe('calculateGoalProgress', () => {
        it('should calculate progress percentage correctly', () => {
            const milestones: Milestone[] = [
                { id: '1', title: 'Milestone 1', completed: true, bananaReward: 10 },
                { id: '2', title: 'Milestone 2', completed: true, bananaReward: 10 },
                { id: '3', title: 'Milestone 3', completed: false, bananaReward: 10 },
                { id: '4', title: 'Milestone 4', completed: false, bananaReward: 10 }
            ];

            const goal: Goal = {
                id: '1',
                title: 'Test Goal',
                milestones,
                createdAt: new Date(),
                totalBananaReward: 40
            };

            expect(calculateGoalProgress(goal)).toBe(50); // 2 out of 4 completed = 50%
        });

        it('should return 0 for goals with no milestones', () => {
            const goal: Goal = {
                id: '1',
                title: 'Test Goal',
                milestones: [],
                createdAt: new Date(),
                totalBananaReward: 0
            };

            expect(calculateGoalProgress(goal)).toBe(0);
        });

        it('should return 100 for fully completed goals', () => {
            const milestones: Milestone[] = [
                { id: '1', title: 'Milestone 1', completed: true, bananaReward: 10 },
                { id: '2', title: 'Milestone 2', completed: true, bananaReward: 10 }
            ];

            const goal: Goal = {
                id: '1',
                title: 'Test Goal',
                milestones,
                createdAt: new Date(),
                totalBananaReward: 20
            };

            expect(calculateGoalProgress(goal)).toBe(100);
        });

        it('should return 0 for goals with no completed milestones', () => {
            const milestones: Milestone[] = [
                { id: '1', title: 'Milestone 1', completed: false, bananaReward: 10 },
                { id: '2', title: 'Milestone 2', completed: false, bananaReward: 10 }
            ];

            const goal: Goal = {
                id: '1',
                title: 'Test Goal',
                milestones,
                createdAt: new Date(),
                totalBananaReward: 20
            };

            expect(calculateGoalProgress(goal)).toBe(0);
        });
    });
});
