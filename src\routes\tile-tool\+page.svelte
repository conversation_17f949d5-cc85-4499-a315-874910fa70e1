<script lang="ts">
  import TilePositioningTool from '$lib/components/TilePositioningTool.svelte';
  import { onMount } from 'svelte';

  let showTool = false;

  onMount(() => {
    showTool = true;
  });
</script>

<svelte:head>
  <title>Tile Positioning Tool - Banana Checklist</title>
</svelte:head>

<div class="tool-page">
  <header class="tool-header">
    <h1>Tile Positioning Tool</h1>
    <div class="tool-info">
      <p>Use this tool to visually position tiles for your game world. Click on the grid to place tiles, then export the code to use in your game.</p>
      <div class="instructions">
        <h3>Instructions:</h3>
        <ul>
          <li><strong>Select Tile ID:</strong> Choose which tile from the tileset to place</li>
          <li><strong>Grid Size:</strong> Adjust the grid snap size (16px = 1x scale, 32px = 2x scale)</li>
          <li><strong>Click to Place:</strong> Click anywhere on the grid to place the selected tile</li>
          <li><strong>Clear All:</strong> Remove all placed tiles</li>
          <li><strong>Export Code:</strong> Generate code you can copy into your game</li>
        </ul>
      </div>
    </div>
    <a href="/" class="back-link">← Back to Game</a>
  </header>

  <TilePositioningTool visible={showTool} />
</div>

<style>
  .tool-page {
    min-height: 100vh;
    background: #2c3e50;
    color: white;
  }

  .tool-header {
    background: #34495e;
    padding: 1rem 2rem;
    border-bottom: 2px solid #3498db;
  }

  .tool-header h1 {
    margin: 0 0 1rem 0;
    color: #3498db;
    font-size: 2rem;
  }

  .tool-info {
    margin-bottom: 1rem;
  }

  .tool-info p {
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
    color: #ecf0f1;
  }

  .instructions {
    background: #2c3e50;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #3498db;
  }

  .instructions h3 {
    margin: 0 0 0.5rem 0;
    color: #3498db;
  }

  .instructions ul {
    margin: 0;
    padding-left: 1.5rem;
  }

  .instructions li {
    margin-bottom: 0.5rem;
    line-height: 1.4;
  }

  .instructions strong {
    color: #e74c3c;
  }

  .back-link {
    display: inline-block;
    padding: 0.5rem 1rem;
    background: #3498db;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    font-weight: 500;
    transition: background-color 0.2s;
  }

  .back-link:hover {
    background: #2980b9;
  }
</style>
