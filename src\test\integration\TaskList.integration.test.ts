import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen } from '@testing-library/svelte';
import userEvent from '@testing-library/user-event';
import { taskStore, userStore } from '$lib/stores';
import TaskList from '$lib/components/TaskList.svelte';
import { get } from 'svelte/store';
import type { Task } from '$lib/types';

describe('TaskList Integration Tests', () => {
    let user: ReturnType<typeof userEvent.setup>;

    beforeEach(() => {
        user = userEvent.setup();

        // Reset stores before each test
        taskStore.clear();
        userStore.set({
            id: 'test-user',
            bananaCount: 100,
            totalTasksCompleted: 0,
            totalGoalsCompleted: 0,
            unlockedFeatures: [],
            isPremium: false,
            createdAt: new Date(),
            lastActiveAt: new Date()
        });
    });

    const createTestTask = (overrides: Partial<Task> = {}): Task => ({
        id: crypto.randomUUID(),
        title: 'Test Task',
        description: 'Test Description',
        category: 'personal',
        priority: 'medium',
        completed: false,
        createdAt: new Date(),
        bananaReward: 10,
        ...overrides
    });

    describe('Empty State', () => {
        it('should show empty state when no tasks exist', () => {
            render(TaskList);

            expect(screen.getByText(/no tasks yet/i)).toBeTruthy();
            expect(screen.getByText(/add your first task/i)).toBeTruthy();
        });

        it('should show task count as 0 when no tasks exist', () => {
            render(TaskList);

            expect(screen.getByText(/0 tasks/i)).toBeTruthy();
        });
    });

    describe('Task Display', () => {
        it('should display tasks when they exist in store', () => {
            const task1 = createTestTask({ title: 'First Task' });
            const task2 = createTestTask({ title: 'Second Task' });

            taskStore.set([task1, task2]);
            render(TaskList);

            expect(screen.getByText('First Task')).toBeTruthy();
            expect(screen.getByText('Second Task')).toBeTruthy();
        });

        it('should show correct task count', () => {
            const tasks = [
                createTestTask({ title: 'Task 1' }),
                createTestTask({ title: 'Task 2' }),
                createTestTask({ title: 'Task 3' })
            ];

            taskStore.set(tasks);
            render(TaskList);

            expect(screen.getByText(/3 tasks/i)).toBeTruthy();
        });

        it('should display task details correctly', () => {
            const task = createTestTask({
                title: 'Important Task',
                description: 'This is very important',
                category: 'work',
                priority: 'high'
            });

            taskStore.set([task]);
            render(TaskList);

            expect(screen.getByText('Important Task')).toBeTruthy();
            expect(screen.getByText('This is very important')).toBeTruthy();
            expect(screen.getByText('work')).toBeTruthy();
            expect(screen.getByText('high')).toBeTruthy();
        });
    });

    describe('Task Completion', () => {
        it('should mark task as completed when checkbox is clicked', async () => {
            const task = createTestTask({ title: 'Completable Task' });
            taskStore.set([task]);

            render(TaskList);

            const checkbox = screen.getByRole('checkbox');
            expect((checkbox as HTMLInputElement).checked).toBe(false);

            await user.click(checkbox);

            const tasks = get(taskStore);
            expect(tasks[0].completed).toBe(true);
            expect(tasks[0].completedAt).toBeInstanceOf(Date);
        });

        it('should award bananas when task is completed', async () => {
            const task = createTestTask({ title: 'Banana Task' });
            taskStore.set([task]);

            render(TaskList);

            const initialBananas = get(userStore).bananaCount;
            const checkbox = screen.getByRole('checkbox');

            await user.click(checkbox);

            const finalBananas = get(userStore).bananaCount;
            expect(finalBananas).toBeGreaterThan(initialBananas);
        });

        it('should show completed tasks with different styling', async () => {
            const task = createTestTask({ title: 'Styled Task' });
            taskStore.set([task]);

            render(TaskList);

            const taskElement = screen.getByText('Styled Task').closest('.task-item');
            expect(taskElement?.className).not.toContain('completed');

            const checkbox = screen.getByRole('checkbox');
            await user.click(checkbox);

            expect(taskElement?.className).toContain('completed');
        });

        it('should allow unchecking completed tasks', async () => {
            const task = createTestTask({
                title: 'Toggle Task',
                completed: true,
                completedAt: new Date()
            });
            taskStore.set([task]);

            render(TaskList);

            const checkbox = screen.getByRole('checkbox');
            expect((checkbox as HTMLInputElement).checked).toBe(true);

            await user.click(checkbox);

            const tasks = get(taskStore);
            expect(tasks[0].completed).toBe(false);
            expect(tasks[0].completedAt).toBeUndefined();
        });
    });

    describe('Task Filtering', () => {
        beforeEach(() => {
            const tasks = [
                createTestTask({ title: 'Pending Work', category: 'work', completed: false }),
                createTestTask({ title: 'Completed Work', category: 'work', completed: true }),
                createTestTask({ title: 'Pending Personal', category: 'personal', completed: false }),
                createTestTask({ title: 'Completed Personal', category: 'personal', completed: true })
            ];
            taskStore.set(tasks);
        });

        it('should show all tasks by default', () => {
            render(TaskList);

            expect(screen.getByText('Pending Work')).toBeTruthy();
            expect(screen.getByText('Completed Work')).toBeTruthy();
            expect(screen.getByText('Pending Personal')).toBeTruthy();
            expect(screen.getByText('Completed Personal')).toBeTruthy();
        });

        it('should filter tasks by completion status', async () => {
            render(TaskList);

            // Filter to show only pending tasks
            const pendingFilter = screen.getByText(/pending/i);
            await user.click(pendingFilter);

            expect(screen.getByText('Pending Work')).toBeTruthy();
            expect(screen.getByText('Pending Personal')).toBeTruthy();
            expect(screen.queryByText('Completed Work')).toBeNull();
            expect(screen.queryByText('Completed Personal')).toBeNull();
        });

        it('should filter tasks by category', async () => {
            render(TaskList);

            // Filter to show only work tasks
            const categoryFilter = screen.getByDisplayValue('all'); // category filter dropdown
            await user.selectOptions(categoryFilter, 'work');

            expect(screen.getByText('Pending Work')).toBeTruthy();
            expect(screen.getByText('Completed Work')).toBeTruthy();
            expect(screen.queryByText('Pending Personal')).toBeNull();
            expect(screen.queryByText('Completed Personal')).toBeNull();
        });
    });

    describe('Task Deletion', () => {
        it('should delete task when delete button is clicked', async () => {
            const task = createTestTask({ title: 'Deletable Task' });
            taskStore.set([task]);

            render(TaskList);

            expect(screen.getByText('Deletable Task')).toBeTruthy();

            const deleteButton = screen.getByRole('button', { name: /delete/i });
            await user.click(deleteButton);

            expect(screen.queryByText('Deletable Task')).toBeNull();

            const tasks = get(taskStore);
            expect(tasks).toHaveLength(0);
        });

        it('should show confirmation before deleting task', async () => {
            const task = createTestTask({ title: 'Confirm Delete Task' });
            taskStore.set([task]);

            // Mock window.confirm
            const confirmSpy = vi.spyOn(window, 'confirm').mockReturnValue(false);

            render(TaskList);

            const deleteButton = screen.getByRole('button', { name: /delete/i });
            await user.click(deleteButton);

            expect(confirmSpy).toHaveBeenCalledWith('Are you sure you want to delete this task?');

            // Task should still exist since confirm returned false
            expect(screen.getByText('Confirm Delete Task')).toBeTruthy();

            confirmSpy.mockRestore();
        });
    });

    describe('Task Sorting', () => {
        beforeEach(() => {
            const now = new Date();
            const tasks = [
                createTestTask({
                    title: 'Old Task',
                    priority: 'low',
                    createdAt: new Date(now.getTime() - 3600000) // 1 hour ago
                }),
                createTestTask({
                    title: 'New Task',
                    priority: 'high',
                    createdAt: now
                }),
                createTestTask({
                    title: 'Medium Task',
                    priority: 'medium',
                    createdAt: new Date(now.getTime() - 1800000) // 30 minutes ago
                })
            ];
            taskStore.set(tasks);
        });

        it('should sort tasks by creation date by default', () => {
            render(TaskList);

            const taskElements = screen.getAllByText(/Task$/);
            expect(taskElements[0].textContent).toContain('New Task'); // Most recent first
            expect(taskElements[1].textContent).toContain('Medium Task');
            expect(taskElements[2].textContent).toContain('Old Task');
        });

        it('should sort tasks by priority when priority sort is selected', async () => {
            render(TaskList);

            const sortSelect = screen.getByDisplayValue(/date/i);
            await user.selectOptions(sortSelect, 'priority');

            const taskElements = screen.getAllByText(/Task$/);
            expect(taskElements[0].textContent).toContain('New Task'); // High priority first
            expect(taskElements[1].textContent).toContain('Medium Task');
            expect(taskElements[2].textContent).toContain('Old Task'); // Low priority last
        });
    });

    describe('Real-time Updates', () => {
        it('should update display when tasks are added to store', async () => {
            render(TaskList);

            expect(screen.getByText(/no tasks yet/i)).toBeTruthy();

            // Add task to store
            const newTask = createTestTask({ title: 'Dynamic Task' });
            taskStore.set([newTask]);

            expect(screen.getByText('Dynamic Task')).toBeTruthy();
            expect(screen.queryByText(/no tasks yet/i)).toBeNull();
        });

        it('should update task count when tasks change', async () => {
            render(TaskList);

            expect(screen.getByText(/0 tasks/i)).toBeTruthy();

            // Add tasks
            const tasks = [
                createTestTask({ title: 'Task 1' }),
                createTestTask({ title: 'Task 2' })
            ];
            taskStore.set(tasks);

            expect(screen.getByText(/2 tasks/i)).toBeTruthy();
        });
    });

    describe('Accessibility', () => {
        it('should have proper ARIA labels and roles', () => {
            const task = createTestTask({ title: 'Accessible Task' });
            taskStore.set([task]);

            render(TaskList);

            expect(screen.getByRole('list')).toBeTruthy();
            expect(screen.getByRole('listitem')).toBeTruthy();
            expect(screen.getByRole('checkbox')).toBeTruthy();
        });

        it('should support keyboard navigation for task completion', async () => {
            const task = createTestTask({ title: 'Keyboard Task' });
            taskStore.set([task]);

            render(TaskList);

            const checkbox = screen.getByRole('checkbox');
            checkbox.focus();

            await user.keyboard(' '); // Space key to toggle checkbox

            const tasks = get(taskStore);
            expect(tasks[0].completed).toBe(true);
        });
    });
});
